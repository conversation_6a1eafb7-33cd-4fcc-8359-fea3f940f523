{"name": "mh-biz", "version": "0.1.0", "private": false, "scripts": {"start": "vite --mode develop.local", "build:es": "cross-env FORMAT=es vite build packages/ --config packages/vite.config.ts", "build:umd": "cross-env FORMAT=umd vite build packages/ --config packages/vite.config.ts", "build:all": "pnpm run build:es && pnpm run build:umd ", "publish:component": "node scripts/publish-component.js", "publish:beta-component": "cross-env PUBLISH_SUFFIX=beta node scripts/publish-component.js", "publish:dev-component": "cross-env PUBLISH_SUFFIX=dev node scripts/publish-component.js", "publish:test-component": "cross-env PUBLISH_SUFFIX=test node scripts/publish-component.js", "publish:all-components": "node scripts/publish-all-components.js", "lint": "eslint . --ext .vue,.js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .vue,.js,.jsx,.ts,.tsx --fix"}, "dependencies": {"@ant-design/icons-vue": "~7.0.1", "@idmy/antd": "~0.0.120", "@idmy/core": "~1.0.144", "@mh-wm/maker": "^1.0.20", "@mh-wm/whole-box": "^1.0.1", "@surely-vue/table": "~5.0.3", "@visactor/vue-vtable": "~1.18.3", "@vueuse/core": "~13.0.0", "ant-design-vue": "~4.2.6", "axios": "~1.8.4", "crypto-js": "~4.2.0", "dayjs": "~1.11.13", "highlight.js": "~11.11.1", "lodash-es": "~4.17.21", "vue": "~3.5.14", "vue-router": "~4.5.1"}, "devDependencies": {"@arco-design/color": "~0.4.0", "@babel/plugin-proposal-class-properties": "~7.18.6", "@babel/plugin-syntax-dynamic-import": "~7.8.3", "@babel/plugin-transform-runtime": "~7.24.7", "@babel/preset-env": "~7.24.8", "@babel/preset-typescript": "~7.24.7", "@commitlint/cli": "~17.6.7", "@commitlint/config-conventional": "~17.6.7", "@types/lodash-es": "~4.17.12", "@types/node": "~18.16.20", "@types/rollup": "~0.54.0", "@typescript-eslint/eslint-plugin": "~8.27.0", "@typescript-eslint/parser": "~8.27.0", "@unocss/preset-attributify": "~66.0.0", "@unocss/preset-icons": "~66.0.0", "@unocss/preset-typography": "~66.0.0", "@unocss/preset-uno": "~66.0.0", "@vitejs/plugin-vue": "~5.0.5", "@vitejs/plugin-vue-jsx": "~3.1.0", "@vue/babel-plugin-jsx": "~1.2.5", "@vue/eslint-config-typescript": "~14.5.0", "autoprefixer": "~10.4.21", "buffer": "~6.0.3", "cross-env": "~7.0.3", "cz-conventional-changelog": "~3.3.0", "eslint": "~9.23.0", "eslint-plugin-vue": "~10.0.1", "flexsearch": "~0.7.43", "less": "~4.2.2", "less-loader": "~12.2.0", "lint-staged": "~13.2.3", "postcss": "~8.4.49", "prettier": "~2.2.1", "shiki": "~3.1.0", "typescript": "~4.9.5", "unocss": "~66.0.0", "unocss-preset-chinese": "~0.3.3", "unocss-preset-ease": "~0.0.4", "unplugin-auto-import": "~0.17.8", "vite": "~6.0.15", "vite-plugin-lazy-import": "~1.0.7", "vite-plugin-monaco-editor": "~1.0.12", "vite-plugin-style-import": "~0.10.1", "vscode-oniguruma": "~2.0.1", "vscode-textmate": "~9.2.0", "vue-tsc": "~1.6.5"}, "peerDependencies": {"vue": "~3.5.13"}, "lint-staged": {"*.{js,ts,jsx,tsx,vue}": ["prettier --write"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}}