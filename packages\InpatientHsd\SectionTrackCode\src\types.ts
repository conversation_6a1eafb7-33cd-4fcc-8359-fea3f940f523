// 病区绑定追溯码组件的类型定义

// 费用明细
export interface FeeDetail {
  keyStr: string // 费用主键，计费主键+计费行号的拼接
  execSeqid: string // 计费主键
  lineNo: number // 计费行号
  visitId: number // 诊疗编号
  oeNo: number // 医嘱行号
  artId: number // 药品编号
  total: number // 计费用量
  unitType: number // 包装类型：1拆零2整包
  bseqid: string // bcs划价单编号
  billLineNo: number // bcs划价单行号
  isDisassembled: number // 拆零标志位
  usedTrackCodeDetails?: UsedTrackCodeDetail[] // 已绑定的追溯码详情
}

// 已使用的追溯码详情
export interface UsedTrackCodeDetail {
  trackCode: string // 追溯码
  usedAmount: number // 使用数量
}

// 患者信息汇总
export interface VisitSummary {
  visitId: string // 诊疗编号
  patientName: string // 患者姓名
  bedNo: string // 床号
  feeDetailList: FeeDetail[] // 费用明细列表
}

// 药品信息汇总
export interface ArtSummary {
  artId: number // 药品编号
  keyStr: string // artId拼接unitType
  artName: string // 药品名称
  artSpec: string // 药品规格
  producer: string // 药品厂家
  packCells: number // 药品拆零系数
  packUnit: string // 药品包装（整包）单位
  cellUnit: string // 药品制剂（拆零）单位
  unitType: number // 包装类型：1拆零2整包
  isDisassembled: number // 拆零标志位
  totalAmount: number // 上报数量
  unitName: string // 根据unitType计算出来的单位
  visitSummaryList: VisitSummary[] // 诊疗信息汇总
}

// 药房追溯码汇总
export interface PharmacyTrackCodeSum {
  artId: number // 药品编号
  trackCode: string // 追溯码
  totalPacks: number // 总整包数
  totalCells: number // 总拆零数
  usedTotalPacks: number // 已使用整包数
  usedTotalCells: number // 已使用拆零数
  curTotalPacks: number // 当前整包数
  curTotalCells: number // 当前拆零数
}

// API返回的数据结构
export interface SectionFeeTrackCodeSummaryResponse {
  artSummaryList: ArtSummary[] // 药品信息汇总列表
  pharmacyTrackCodeSums: PharmacyTrackCodeSum[] // 药房追溯码汇总列表
}

// 追溯码录入区域的数据结构
export interface TrackCodeItem {
  trackCode: string // 追溯码
  cellsDispensed: number // 已用制剂数
  cellsRemain: number // 剩余制剂数
  isDisassembled: boolean // 拆零标志位
  isUsed?: boolean // 是否已使用（用于控制是否可以解绑）
}

// 患者卡片数据
export interface PatientCard {
  visitId: string // 诊疗编号
  patientName: string // 患者姓名
  bedNo: string // 床号
  needAmount: number // 需求量
  boundTrackCodes: TrackCodeItem[] // 绑定的追溯码列表
  isCompleted: boolean // 是否完成绑定
}

// 药品卡片数据
export interface DrugCard {
  artId: number // 药品编号
  keyStr: string // 唯一标识
  artName: string // 药品名称
  artSpec: string // 药品规格
  producer: string // 药品厂家
  unitType: number // 包装类型
  totalAmount: number // 需求量
  unitName: string // 单位名称
  patients: PatientCard[] // 关联的患者列表
  isCompleted: boolean // 是否完成绑定
  pharmacyTrackCodes: PharmacyTrackCodeSum[] // 药房预绑定的追溯码
}

// 表格模型接口
export interface TableModel {
  loading?: boolean
  columns: any[]
  dataSource: any[]
  selectedRowKeys?: any[]
  rowSelection?: any
  pagination: any
  loadDataSource?: () => Promise<void>
  selectRow?: (record: any) => void
  customRow?: (record: any) => any
}
