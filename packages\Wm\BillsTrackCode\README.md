# BillsTrackCode 多单据追溯码扫描组件

多单据追溯码扫描组件，用于录入和管理多个单据药品的追溯码信息。该组件基于 RecipeTrackCode 处方扫码组件构建，专门处理多个单据的批量追溯码采集。

## 🚀 核心特性

- 支持多个单据的批量追溯码采集
- 支持通过扫码枪或高拍仪录入追溯码
- 支持追溯码有效性验证
- 支持拆零药品的追溯码录入
- 支持采集结果状态显示
- 智能药品汇总：按条目类别和拆零/整包类型自动汇总
- **🎯 智能分配算法**：追溯码按需智能分配到具体明细，避免超量绑定
- 基于ant-design-vue的组件实现

## ⚠️ 重要提醒：智能分配机制

### 核心问题
在多单据追溯码采集中，一个药品可能由多个明细构成，需要智能分配追溯码到具体明细：

```
药品：阿莫西林拆零 (汇总显示：totalCells=7)
├── 明细1: wbSeqId=1001, lineNo=1, totalCells=3
├── 明细2: wbSeqId=1001, lineNo=2, totalCells=2
└── 明细3: wbSeqId=1002, lineNo=1, totalCells=2
```

### 智能分配策略
当绑定4个拆零追溯码时，组件会：
1. **分析需求**：计算每个明细的剩余需求量
2. **智能分配**：明细1分配3个，明细2分配1个
3. **API调用**：调用多次addCode API，确保每个明细获得正确数量
4. **避免问题**：不会将所有4个码都绑定给第一个明细

### 分配结果示例
```javascript
// 实际API调用
addCodeApi({ wbSeqid: 1001, lineNo: 1, totalCells: 3 }) // 明细1：3个
addCodeApi({ wbSeqid: 1001, lineNo: 2, totalCells: 1 }) // 明细2：1个
// 明细3暂时不分配，等待下次扫码
```

**这确保了每个明细获得恰好需要的数量，避免了数据不一致的问题。**

详细的算法说明请参见 [智能分配算法详解](#智能分配算法详解) 章节。

## 📋 目录

- [重要提醒：智能分配机制](#重要提醒智能分配机制)
- [核心概念](#核心概念)
- [API 调用说明](#api-调用说明)
- [使用方法](#使用方法)
- [重要技术说明](#重要技术说明)
  - [响应式更新机制的关键修复](#响应式更新机制的关键修复)
  - [智能分配算法详解](#智能分配算法详解)
- [版本历史](#版本历史)

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-wm/util`: 工具库，提供API调用

## 核心概念

### 多单据处理逻辑

与单一处方不同，多单据追溯码扫描需要处理来自不同单据的药品明细。由于不同单据中的明细必然存在 `artId` 有交集的部分，组件采用智能汇总策略：

#### 汇总规则

药品明细按以下规则进行汇总：

1. **拆零类汇总条件**：
   - `artId + totalCells > 0` 或
   - `totalPacks > 0` 且标记了 `artIsDisassembled` 允许拆零上报

2. **整包类汇总条件**：
   - `totalPacks > 0` 且不被标记 `artIsDisassembled` 拆零上报

#### 汇总后排序

每个条目按上面的汇总后最多出现一列拆零一列整包，大多数只会有一列，再根据以下顺序排列：
1. `artTypeId` 升序
2. `artId` 升序
3. 有整包的优先
4. 有拆零的次之

#### 追溯码绑定策略

- **左侧列表**：显示汇总后的药品条目，根据汇总后的整包数拆零数进行追溯码绑定
- **右侧列表**：显示与汇总条目关联的追溯码，但调用 `addCodeApi`、`delCodeApi` 时需要细分成原始的明细主键 `wbSeqid` 和 `lineNo`
- **批量删除**：`delAllCodeApi` 能接收 `{ wbSeqids: wbSeqIdLs }`

### 重要数据结构

#### 单据明细 (billDetail)

每个单据明细包含以下关键属性：

- `wbSeqid`: 业务流水号，对应入参单据id列表中的一个
- `lineNo`: 单据明细的行号，表示这个药品在单据的第几行
- `artId`: 条目编号
- `artTypeId`: 条目类别
- `artName`: 药品名称
- `totalPacks`: 整包数量
- `totalCells`: 拆零数量
- `artIsDisassembled`: 是否允许拆零上报

#### 汇总后的数据结构

汇总后的左侧列表每行包含：

- 原始明细的所有属性
- `wbSeqid`: **必须返回**，用于与右侧追溯码列表关联
- `sourceDetails`: 原始明细列表，用于API调用时拆分
- `aggregatedTotalPacks`: 汇总后的整包数量
- `aggregatedTotalCells`: 汇总后的拆零数量

### 拆零系数

拆零系数（packCells）记录整包与拆零数量的转换系数。例如药品规格为1盒里装5支，packCells=5，表示1盒药（整包totalPacks）= 5支（拆零数totalCells）。

### 追溯码

每盒药都有一个唯一的追溯码，用于药品追溯管理。药品追溯码的长度通常在19位到27位之间，小于19位的码（如69开头的13位条形码）不是有效的追溯码，组件会给出警告提示，不能用于业务绑定。

市面上常见的是20位溯源码，其中前7位是药品标识，能标识一个药品的唯一性，与系统中的artId（条目编号）能够对应。未来数据健全后，组件将支持校验artId和追溯码前7位的对应关系，确保追溯码与药品匹配。

追溯码前7位是药品的唯一标志，一种药需要发5盒的话，这个药扫进来的五个码前7位应该是相同的。

### 追溯码拆零标志

口服药一般按整盒发药，注射药有1盒多支的情况，多支拆零数共用1盒的一个溯源码，但会记录拆零标志位。

### 追溯码扫描

在摆药时扫描一个或多个溯源码对应一条医嘱的需求量。住院的注射药按拆零数执行，可能出现多支注射药扫同一个码的情况，也可能出现一个患者医嘱的用药需求跨多个追溯码的情况。

药品数量分为整包数量(totalPacks)和拆零数量(totalCells)两种计量方式：
- 整包数量：以药品包装单位计量，如"盒"、"瓶"等
- 拆零数量：以药品最小单位计量，如"片"、"支"等

扫描追溯码时，系统会根据药品的整包数量和拆零数量进行响应。例如，如果一种药品需要发5片，而一盒有10片，则可能出现以下情况：
1. 使用一个追溯码，设置拆零数量为5
2. 如果上一盒只剩3片，则需要使用两个追溯码，一个设置拆零数量为3，另一个设置拆零数量为2

### 外部设备

追溯码的扫描通过外部设备（扫码枪或高拍仪）进行，识别后将文本传到组件里。扫码枪扫一个码将文本写入并触发回车事件，高拍仪可能一个追溯码对应一个回车事件，在文本文档查看的效果就是5个追溯码5行。

## API 调用说明

### 主要 API 接口

1. **wmBillDetailListApi** - 获取单据明细列表
   - 入参：`{ wbSeqids: number[] }`
   - 返回：单据明细数组

2. **trackCodeListByWbSeqIdsApi** - 获取追溯码列表
   - 入参：`{ wbSeqids: number[] }`
   - 返回：追溯码数组

3. **trackCodeAddCodeApi** - 添加追溯码
   - 入参：`{ wbSeqid, lineNo, trackCode, isDisassembled, totalCells }`

4. **trackCodeDelCodeApi** - 删除追溯码
   - 入参：`{ wbSeqid, lineNo, trackCode }`

5. **trackCodeDelCodeByWbSeqIdApi** - 批量删除追溯码
   - 入参：`{ wbSeqids: number[] }`

### 性能优化的新 API 接口

为了提高性能，以下4个API已从循环调用的方式改为基于artId的单次调用：

6. **articleSetNoTrackCodeApi** - 设置无追溯码（新）
   - 入参：`{ artId: number }`
   - 说明：替代原来的 `trackCodeSetNoTrackCodeApi` 循环调用

7. **articleClearNoTrackCodeApi** - 清除无追溯码（新）
   - 入参：`{ artId: number }`
   - 说明：替代原来的 `trackCodeClearNoTrackCodeApi` 循环调用

8. **articleSetDisassembledApi** - 设置拆零上报（新）
   - 入参：`{ artId: number }`
   - 说明：替代原来的 `trackCodeSetDisassembledApi` 循环调用

9. **articleClearDisassembledApi** - 清除拆零上报（新）
   - 入参：`{ artId: number }`
   - 说明：替代原来的 `trackCodeClearDisassembledApi` 循环调用

### 性能优化说明

- **旧方式**：需要为每个原始明细（wbSeqid + lineNo）单独调用API，当一个药品在多个单据中出现时，需要多次API调用
- **新方式**：只需要传入artId，后端会自动处理该药品在所有相关单据中的状态，大大减少了API调用次数
- **性能提升**：从O(n)次API调用优化为O(1)次API调用，其中n为该药品在不同单据中的明细数量

## 打包与安装

### 打包组件

在开发完成后，需要打包组件以便发布和使用：

```bash
# 在项目根目录下执行打包命令
pnpm publish:component Wm/BillsTrackCode
```

这个命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

### 安装组件

在其他项目中安装该组件：

```bash
# 使用npm安装
npm install @mh-wm/bills-track-code

# 或使用pnpm安装
pnpm add @mh-wm/bills-track-code
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-wm/bills-track-code": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import '@mh-wm/bills-track-code/index.css'
```

### 基本用法 - 按钮触发

```vue
<template>
  <!-- 基础用法 - 显示按钮 -->
  <BillsTrackCode
    :wbSeqIdLs="wbSeqIdLs"
    @success="handleSuccess"
  />
</template>

<script setup>
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import '@mh-wm/bills-track-code/index.css'
import { ref } from 'vue'

// 单据ID列表
const wbSeqIdLs = ref([12345, 12346, 12347])

// 成功回调
const handleSuccess = (data) => {
  console.log('多单据扫码录入成功', data)
}
</script>
```

### 通过js方法调用

```vue
<template>
  <!-- 不显示按钮，通过js方法调用 -->
  <BillsTrackCode
    ref="billsTrackCodeRef"
    :showButton="false"
    @success="handleSuccess"
  />

  <!-- 自定义按钮 -->
  <Button @click="openBillsTrackCode">打开多单据扫码录入</Button>
</template>

<script setup>
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import '@mh-wm/bills-track-code/index.css'
import { Button } from 'ant-design-vue'
import { ref } from 'vue'

// 组件引用
const billsTrackCodeRef = ref()

// 单据ID列表
const wbSeqIdLs = ref([12345, 12346, 12347])

// 打开多单据扫码录入窗口
const openBillsTrackCode = () => {
  billsTrackCodeRef.value.open(wbSeqIdLs.value)
}

// 成功回调
const handleSuccess = (data) => {
  console.log('多单据扫码录入成功', data)
}
</script>
```

## 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| modalWidth | 对话框宽度 | number \| string | 1400 |
| onlyAddRecognizedTrackCode | 是否只添加识别追溯码 | boolean | false |
| enableOnlyAddRecognizedTrackCodeOption | 是否启用"只添加识别追溯码"复选框 | boolean | false |
| enableAutoClosePrompt | 是否启用自动关闭提示 | boolean | false |
| isView | 是否为查看模式 | boolean | false |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 多单据扫码录入成功时触发 | (data: any) => void |
| cancel | 取消多单据扫码录入时触发 | () => void |

## 组件方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开多单据扫码录入窗口 | (wbSeqIdLs?: number[]) => void |
| close | 关闭多单据扫码录入窗口 | () => void |

## 开发与使用流程

### 开发流程

1. **组件开发**：在`packages/Wm/BillsTrackCode/src`目录下开发组件
   ```
   packages/
   └── Wm/
       └── BillsTrackCode/
           ├── src/
           │   ├── index.vue      # 主组件
           │   └── ...            # 其他子组件或工具
           ├── index.ts           # 组件入口文件
           ├── package.json       # 组件包配置
           └── README.md          # 组件文档
   ```

2. **本地测试**：在`src/views/wm/examples`目录下创建示例页面进行测试
   ```vue
   <!-- src/views/wm/examples/BillsTrackCodeExample.vue -->
   <template>
     <div>
       <h1>多单据追溯码扫描组件示例</h1>
       <BillsTrackCode
         ref="billsTrackCodeRef"
         :modalWidth="1400"
         @success="handleSuccess"
         @cancel="handleCancel"
       />
       <Button type="primary" @click="openTrackCode">打开多单据扫码</Button>
     </div>
   </template>

   <script setup>
   import { ref } from 'vue'
   import { Button, message } from 'ant-design-vue'
   import BillsTrackCode from '@/packages/Wm/BillsTrackCode/src/index.vue'

   const billsTrackCodeRef = ref()

   const openTrackCode = () => {
     // 示例参数：多个单据ID
     const wbSeqIdLs = [123456, 123457, 123458]

     billsTrackCodeRef.value.open(wbSeqIdLs)
   }

   const handleSuccess = (data) => {
     message.success(`多单据扫码成功，处理了 ${data.length} 个单据`)
   }

   const handleCancel = () => {
     message.info('取消多单据扫码')
   }
   </script>
   ```

3. **打包组件**：开发完成后，执行打包命令
   ```bash
   pnpm publish:component Wm/BillsTrackCode
   ```

### 使用流程

1. **安装组件**：在项目中安装组件
   ```bash
   pnpm add @mh-wm/bills-track-code
   ```

2. **引入组件**：在Vue文件中引入组件
   ```vue
   <template>
     <div>
       <BillsTrackCode
         ref="billsTrackCodeRef"
         :modalWidth="1400"
         @success="handleSuccess"
         @cancel="handleCancel"
       />
       <Button type="primary" @click="openTrackCode">打开多单据扫码</Button>
     </div>
   </template>

   <script setup>
   import { ref } from 'vue'
   import { Button, message } from 'ant-design-vue'
   import { BillsTrackCode } from '@mh-wm/bills-track-code'
   import '@mh-wm/bills-track-code/index.css'

   const billsTrackCodeRef = ref()

   const openTrackCode = () => {
     // 实际业务参数：多个单据ID
     const wbSeqIdLs = [123456, 123457, 123458] // 实际业务中的单据ID列表

     billsTrackCodeRef.value.open(wbSeqIdLs)
   }

   const handleSuccess = (data) => {
     message.success(`多单据扫码成功，处理了 ${data.length} 个单据`)
     // 在这里处理成功后的业务逻辑
   }

   const handleCancel = () => {
     message.info('取消多单据扫码')
     // 在这里处理取消后的业务逻辑
   }
   </script>
   ```

## 重要技术说明

### 响应式更新机制的关键修复

在开发过程中遇到了一个重要的响应式更新问题，值得后续开发者重点关注：

#### 问题描述
- **现象**：右侧表格绑定追溯码后，左侧汇总表格的已采数量和采集结果不会实时更新
- **触发条件**：只有在手动切换选中行或自动跳转下一行时，左侧表格才会更新
- **根本原因**：左侧表格使用的是按artId+type汇总后的数据（`aggregatedBillDetails` computed属性），而采集数量的计算逻辑在独立的`updateCollectedCounts`函数中，导致Vue的响应式链断裂

#### 技术根源
```javascript
// 问题代码模式（已修复）
const aggregatedBillDetails = computed(() => {
  // 基于原始数据进行汇总
  return aggregatedData
})

const updateCollectedCounts = () => {
  // 直接修改computed返回的数组
  aggregated.forEach(drug => {
    drug.collectedPacks = newValue // ❌ 这种修改不会触发响应式更新
  })
}
```

#### 解决方案
通过引入响应式更新触发器，将采集数量计算逻辑集成到computed属性中：

```javascript
// 修复后的代码模式
const updateTrigger = ref(0)

const aggregatedBillDetails = computed(() => {
  // 依赖更新触发器，确保响应式更新
  updateTrigger.value

  // 汇总逻辑...

  // 在computed中计算采集数量和冲突检测
  if (trackCodeDetailTableModel.allTrackCodes?.length > 0) {
    // 计算每个药品的采集数量
    // 检测前缀冲突
  }

  return result
})

const updateCollectedCounts = () => {
  // 触发响应式更新，让aggregatedBillDetails重新计算
  updateTrigger.value++
}
```

#### 关键要点
1. **数据计算集中化**：将所有影响汇总数据的计算逻辑都放在computed属性中
2. **响应式依赖建立**：通过更新触发器建立明确的响应式依赖关系
3. **避免直接修改computed结果**：不要直接修改computed属性返回的对象或数组
4. **性能考虑**：只有在数据真正变化时才触发重新计算

#### 适用场景
这个解决方案适用于所有涉及以下情况的Vue组件：
- 使用computed属性进行数据汇总或转换
- 需要基于外部数据变化更新computed结果
- 多个数据源影响同一个computed属性

#### 开发建议
- 在开发类似汇总组件时，优先考虑将所有计算逻辑集中到computed属性中
- 避免在computed属性外部修改其返回的数据结构
- 使用响应式触发器来控制computed属性的重新计算时机
- 充分测试数据变化后的界面更新效果

### 智能分配算法详解

#### 问题背景
在多单据追溯码采集中，一个药品可能由多个明细构成：
```
药品：阿莫西林拆零 (aggregatedKey: "123_cells")
├── 明细1: wbSeqId=1001, lineNo=1, totalCells=3
├── 明细2: wbSeqId=1001, lineNo=2, totalCells=2
└── 明细3: wbSeqId=1002, lineNo=1, totalCells=2
汇总后：totalCells=7
```

#### 分配策略

**整包模式**：
- 选择第一个有整包数量的明细进行绑定
- 一个追溯码对应一个整包，绑定到单个明细

**拆零模式**：
1. **需求分析**：计算每个明细的剩余需求量
   ```javascript
   剩余需求 = 明细.totalCells - 已采集数量
   ```

2. **优先级排序**：按剩余需求量从大到小排序明细

3. **智能分配**：
   ```javascript
   绑定4个拆零码的分配过程：
   明细1需要3个 → 分配3个 → 剩余1个
   明细2需要2个 → 分配1个 → 剩余0个
   ```

4. **API调用**：
   ```javascript
   addCodeApi({ wbSeqid: 1001, lineNo: 1, totalCells: 3 })
   addCodeApi({ wbSeqid: 1001, lineNo: 2, totalCells: 1 })
   ```

#### 核心算法
```javascript
const allocateTrackCodeToDetails = (currentArt, isDisassembled, totalCells) => {
  // 获取每个明细的已采集数量
  const detailCollectedMap = new Map()

  // 按剩余需求量排序明细
  const sortedDetails = currentArt.sourceDetails
    .filter(detail => detail.totalCells > 0)
    .sort((a, b) => {
      const aRemaining = a.totalCells - (detailCollectedMap.get(a.key) || 0)
      const bRemaining = b.totalCells - (detailCollectedMap.get(b.key) || 0)
      return bRemaining - aRemaining
    })

  // 智能分配
  let remainingCells = totalCells
  const allocations = []

  for (const detail of sortedDetails) {
    const needed = detail.totalCells - collected
    const allocatedToThis = Math.min(remainingCells, needed)

    if (allocatedToThis > 0) {
      allocations.push({
        wbSeqid: detail.wbSeqid,
        lineNo: detail.lineNo,
        allocatedCells: allocatedToThis
      })
      remainingCells -= allocatedToThis
    }
  }

  return { success: true, allocations }
}
```

#### 特殊场景处理

**拆零上报整包药品**（如`totalPacks=1, artIsDisassembled=1`）：

这种情况下，药品有整包数量但被标记为拆零上报，需要特殊处理：

```javascript
// 明细过滤逻辑
.filter((detail: any) => {
  // 有拆零数量的明细
  if ((detail.totalCells || 0) > 0) return true

  // 被标记为拆零上报且有整包数量的明细
  if (detail.artIsDisassembled && (detail.totalPacks || 0) > 0) return true

  return false
})

// 需求量计算逻辑
if (detail.artIsDisassembled && (detail.totalPacks || 0) > 0 && (detail.totalCells || 0) === 0) {
  // 拆零上报的整包药品：需求量 = 整包数量 - 已采集拆零数量
  needed = (detail.totalPacks || 0) - collected
} else {
  // 普通拆零药品：需求量 = 拆零数量 - 已采集拆零数量
  needed = (detail.totalCells || 0) - collected
}
```

**修复前的问题**：
- 扫描拆零追溯码时提示"未找到可绑定拆零的明细"
- 智能分配算法只考虑`totalCells > 0`的明细，忽略了拆零上报的整包药品

**修复后的效果**：
- 正确识别拆零上报的整包药品为可绑定明细
- 需求量计算基于整包数量而不是拆零数量
- 智能分配算法正确处理这种特殊场景

#### 实际效果
- **精确分配**：每个明细获得恰好需要的数量
- **避免浪费**：不会将所有追溯码绑定到第一个明细
- **支持超量**：如有剩余数量，分配给第一个明细
- **实时计算**：基于当前已采集状态进行分配

#### 开发者注意事项
1. **API调用变化**：由于智能分配，一次追溯码绑定可能产生多次API调用
2. **性能考虑**：分配算法会遍历所有明细，但性能影响很小
3. **错误处理**：如果某个明细的API调用失败，整个绑定操作会回滚
4. **数据一致性**：分配算法确保数据的一致性，避免了手动分配的错误
5. **调试支持**：控制台会输出详细的分配过程，便于调试

#### 业务场景适用性
- **适用**：多单据、多明细的复杂追溯码采集场景
- **适用**：需要精确控制每个明细采集数量的场景
- **适用**：拆零药品的精细化管理场景
- **不适用**：简单的单一明细追溯码绑定（建议使用RecipeTrackCode）

### 追溯码使用状态管理

#### 功能说明
组件支持追溯码的使用状态管理，通过`used`属性控制追溯码的删除权限：

```javascript
// API返回的追溯码数据结构
{
  trackCode: "81122981263916924481",
  wbSeqid: 1001,
  lineNo: 1,
  used: true,  // 新增：使用状态标志
  // ...其他属性
}
```

#### 状态说明
- **used: true**：追溯码已被使用，不允许删除
- **used: false** 或 **未设置**：追溯码未被使用，允许删除

#### 界面表现
1. **已使用的追溯码**：
   - 操作列显示绿色"已使用"文本
   - 不显示删除按钮
   - 优先显示在列表最上方
   - 提供视觉提醒，避免用户困惑

2. **未使用的追溯码**：
   - 操作列显示红色"删除"按钮
   - 显示在已使用追溯码之后
   - 允许正常删除操作

#### 排序规则
```javascript
// 追溯码列表排序逻辑
trackCodeDetailTableModel.dataSource = filteredCodes.sort((a, b) => {
  // 已使用的排在前面
  if (a.used && !b.used) return -1
  if (!a.used && b.used) return 1

  // 如果使用状态相同，按追溯码字符串排序
  return a.trackCode.localeCompare(b.trackCode)
})
```

#### 安全机制
```javascript
const onDelArt = async (record: any) => {
  // 检查追溯码是否已被使用
  if (record.used) {
    message.warning('该追溯码已被使用，不允许删除')
    return
  }
  // 执行删除操作...
}
```

#### 使用场景
- **药房发药**：已发给患者的追溯码标记为used=true
- **退费处理**：退费时需要删除追溯码，但已使用的不能删除
- **数据审计**：保护已使用的追溯码数据完整性

## 注意事项

1. **多单据汇总逻辑**：组件会自动将多个单据中相同 `artId` 的药品进行汇总，按拆零/整包类型分别处理。
2. **追溯码长度验证**：组件会验证扫描的追溯码长度，只有19位到27位之间的码才被视为有效追溯码。
3. **追溯码与药品匹配**：追溯码前7位是药品标识，组件会校验追溯码与药品的匹配关系。
4. **原始明细映射**：虽然左侧显示汇总后的数据，但API调用时会自动拆分回原始的 `wbSeqid` 和 `lineNo`。
5. **智能药品切换**：当满足以下条件时，组件会自动开始扫描下一种药品：
   - 当前药品的整包数量和拆零数量都已满足
   - 扫描的追溯码没有被其他药品绑定
6. **异常处理**：当一种药品需要扫描多盒但扫描出了多种不同前7位的追溯码时，组件会给出颜色提醒。
7. **拆零上报管理**：支持"拆零上报"功能，适用于大输液等一箱一码的情况。
8. **无追溯码管理**：支持将药品设置为"无追溯码"状态。
9. **高拍仪优化**：支持高拍仪快速连续扫码，异步处理追溯码数据。
10. **自动聚焦**：每次进入页面和操作完成后都会自动聚焦到追溯码输入框。

## 与 RecipeTrackCode 的主要区别

| 特性 | RecipeTrackCode | BillsTrackCode |
| --- | --- | --- |
| 处理范围 | 单一处方 | 多个单据 |
| 入参 | `(title, wbSeqid, visitId, isView)` | `(wbSeqIdLs)` |
| 数据汇总 | 无需汇总 | 按 artId + 拆零/整包类型汇总 |
| API调用 | 单个 wbSeqid | 数组 wbSeqids |
| 左侧列表 | 直接显示明细 | 显示汇总后的数据 |
| 追溯码关联 | lineNo | wbSeqid + lineNo |

## 版本历史

### v1.2.4 (拆零上报整包药品修复版本)
- **拆零上报修复**：修复totalPacks=1且artIsDisassembled=1的药品无法绑定拆零追溯码的问题
- **智能分配增强**：支持被标记为拆零上报的整包药品的追溯码分配
- **需求量计算优化**：对于拆零上报的整包药品，需求量等于整包数量而不是拆零数量
- **过滤逻辑完善**：明细过滤逻辑同时考虑拆零数量和拆零上报标记

### v1.2.3 (追溯码排序优化版本)
- **排序优化**：已使用的追溯码优先显示在右侧列表最上方，便于用户快速识别
- **用户体验提升**：重新打开页面时，已使用的追溯码排在前面，未使用的排在后面
- **排序规则**：先按使用状态排序（已使用优先），再按追溯码字符串排序

### v1.2.2 (追溯码使用状态管理版本)
- **使用状态管理**：支持追溯码的used属性，已使用的追溯码不允许删除
- **界面优化**：已使用的追溯码显示绿色"已使用"文本，未使用的显示删除按钮
- **安全保护**：在删除函数中增加used状态检查，防止误删已使用的追溯码
- **API兼容**：兼容trackCodeListByWbSeqIdsApi返回的used属性

### v1.2.1 (排序兼容优化版本)
- **排序兼容性**：修复自动切换下一行功能，现在基于表格当前排序顺序查找下一行
- **用户体验优化**：解决了在拆零优先排序模式下，整包完成后错误跳转到拆零上报条目的问题
- **逻辑改进**：checkAndSwitchToNextDrug方法现在使用filteredBillDetails（排序后的数组）而不是原始汇总数组

### v1.2.0 (智能分配优化版本)
- **核心功能**：实现追溯码智能分配到具体明细的算法
- **分配策略**：根据明细的实际需求量和已采集数量，智能分配追溯码到最合适的明细行
- **多明细支持**：正确处理一个药品由多个明细（wbSeqId+lineNo）构成的情况
- **拆零分配优化**：拆零追溯码按需分配，避免超量绑定到单个明细
- **API调用优化**：根据分配结果调用多次addCode API，确保每个明细获得正确的数量

### v1.1.0 (响应式更新优化版本)
- **重要修复**：解决了左侧汇总表格不能实时响应右侧追溯码变化的问题
- **技术改进**：将采集数量计算逻辑集成到computed属性中，确保响应式更新
- **性能优化**：使用响应式触发器机制，避免不必要的重复计算
- **前缀冲突检测**：优化同一前缀绑定给不同药品的异常检测逻辑
- **拆零数量自动计算**：参考RecipeTrackCode实现智能剩余数量计算
- **开发体验提升**：在README中添加详细的技术说明，帮助后续开发者避免类似问题

### v1.0.0 (初始版本)
- 基于 RecipeTrackCode v1.2.0 构建
- 支持多单据批量追溯码采集
- 智能药品汇总：按条目类别和拆零/整包类型自动汇总
- 支持原始明细映射，API调用时自动拆分
- 支持所有 RecipeTrackCode 的核心功能：
  - 追溯码扫描、验证、绑定
  - 拆零药品管理
  - 追溯码长度验证(19-27位)
  - 智能药品匹配
  - 全部清除功能
  - 自动完成提示
  - 无追溯码管理
  - 拆零上报管理
  - 高拍仪支持
