export function changeBatchNoApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmbill/saveChangeBatchNo', params, { appKey: 'wm' })
}

export function wmBillInfoApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmbill/info', params, { appKey: 'wm' })
}

export function wmBillDetailPageApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmbill/detailPage', params, { appKey: 'wm' })
}

export function wmBillDetailListApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmbill/detailLsByIds', params, { appKey: 'wm' })
}

export function splitPackApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmbill/performSplitPack', params, { appKey: 'wm' })
}
