# WholeBox 组件打包服务文档

## 概述

WholeBox 组件已配置完整的打包服务，支持自动化构建、类型生成和发布流程。

## 打包配置

### package.json 脚本

```json
{
  "scripts": {
    "publish:build": "cd ../../ && pnpm run publish:component Wm/WholeBox --no-publish",
    "publish:component": "cd ../../ && pnpm run publish:component Wm/WholeBox"
  }
}
```

### 构建产物

打包后会生成以下文件结构：

```
dist/Wm/WholeBox/
├── es/
│   └── index.js          # ES模块格式
├── src/
│   ├── index.d.ts        # 组件类型声明
│   └── index.vue.d.ts    # Vue组件类型声明
├── examples/
│   └── Basic.vue.d.ts    # 示例文件类型声明
├── index.d.ts            # 主入口类型声明
├── index.css             # 样式文件
└── README.md             # 文档文件
```

## 使用方法

### 1. 仅构建（不发布）

```bash
# 在组件目录下
cd packages/Wm/WholeBox
pnpm run publish:build

# 或在项目根目录下
pnpm publish:component Wm/WholeBox --no-publish
```

### 2. 构建并发布

```bash
# 在组件目录下
cd packages/Wm/WholeBox
pnpm run publish:component

# 或在项目根目录下
pnpm publish:component Wm/WholeBox
```

### 3. 发布不同版本

```bash
# 发布开发版本
pnpm publish:dev-component Wm/WholeBox

# 发布测试版本
pnpm publish:test-component Wm/WholeBox

# 发布Beta版本
pnpm publish:beta-component Wm/WholeBox
```

## 构建流程

1. **源码编译**: 将 TypeScript/Vue 源码编译为 JavaScript
2. **类型生成**: 生成 TypeScript 类型声明文件
3. **样式处理**: 处理 Less/CSS 样式并生成独立的 CSS 文件
4. **依赖处理**: 处理 workspace 依赖，转换为正确的版本号
5. **文件复制**: 复制必要的文档和配置文件
6. **版本管理**: 自动更新版本号
7. **发布**: 发布到内部 npm 仓库

## 构建测试

运行测试脚本验证打包服务：

```bash
cd packages/Wm/WholeBox
node test-build.js
```

测试内容包括：
- ✅ 源文件完整性检查
- ✅ 构建流程执行
- ✅ 构建产物验证
- ✅ 脚本配置检查
- ✅ 依赖配置验证

## 构建产物说明

### ES模块 (es/index.js)
- 大小: ~19KB (压缩后 ~3.3KB)
- 格式: ES2015+
- 包含: 完整的组件逻辑和模板

### 类型声明 (index.d.ts)
- 提供完整的 TypeScript 类型支持
- 导出组件实例类型
- 支持 IDE 智能提示

### 样式文件 (index.css)
- 大小: ~200B
- 包含组件的 scoped 样式
- 支持按需加载

## 依赖管理

### peerDependencies (12个)
- Vue 3.x 生态系统
- Ant Design Vue 4.x
- 工具库和核心依赖

### dependencies (2个)
- @mh-wm/util: 工具函数库
- @mh-wm/pharmacy: 业务组件库

## 发布配置

```json
{
  "name": "@mh-wm/WholeBox",
  "version": "1.0.1",
  "type": "module",
  "main": "umd/index.js",
  "module": "es/index.js",
  "types": "index.d.ts",
  "style": "index.css",
  "publishConfig": {
    "access": "public"
  }
}
```

## 故障排除

### 常见问题

1. **构建失败**: 检查依赖是否正确安装
2. **类型错误**: 确保 TypeScript 配置正确
3. **样式问题**: 检查 Less 预处理器配置
4. **发布失败**: 验证 npm 仓库配置和权限

### 调试命令

```bash
# 检查构建环境
pnpm --version
node --version

# 清理缓存
pnpm store prune

# 重新安装依赖
rm -rf node_modules
pnpm install
```

## 最佳实践

1. **版本管理**: 遵循语义化版本规范
2. **依赖更新**: 定期更新依赖版本
3. **测试验证**: 构建前运行测试脚本
4. **文档同步**: 保持文档与代码同步
5. **变更记录**: 记录重要变更和版本历史
