# WholeBox 拆零盒整组件

拆零盒整组件是一个用于处理药品库存整包数与拆零数转换的Vue组件，支持仓库总库存和批次库存的双向联动计算。

## 功能特性

- ✅ **拆零盒整功能** - 支持整包数与拆零数的互相转换
- ✅ **双向联动计算** - 修改整包数自动计算拆零数，反之亦然
- ✅ **实时数据校验** - 确保输入数据的准确性和总量不变
- ✅ **API接口集成** - 调用后端接口进行数据持久化
- ✅ **用户友好界面** - 清晰的提示信息和操作反馈
- ✅ **TypeScript支持** - 完整的类型定义

## 安装

```bash
pnpm add @mh-wm/WholeBox
```

## 基本用法

```vue
<template>
  <div>
    <!-- 拆零盒整按钮 -->
    <a-button @click="handleSplitPack">拆零盒整</a-button>

    <!-- 拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WholeBox from '@mh-wm/WholeBox'

const wholeBoxRef = ref()

// 库存记录数据
const stockRecord = {
  artName: '阿莫西林胶囊',
  artSpec: '0.25g',
  producer: '华北制药',
  packCells: 10,        // 包装规格：10粒/盒
  cellUnit: '粒',       // 拆零单位
  packUnit: '盒',       // 包装单位
  deptTotalPacks: 5,    // 仓库总库存整包数
  deptTotalCells: 3,    // 仓库总库存拆零数
  totalPacks: 2,        // 批次库存整包数
  totalCells: 8,        // 批次库存拆零数
}

const handleSplitPack = () => {
  wholeBoxRef.value?.handleSplitPack(stockRecord)
}
</script>
```

## API

### Props

该组件不接受任何props，通过方法调用的方式使用。

### Methods

#### handleSplitPack(record)

打开拆零盒整录入模态框。

**参数：**

- `record` (Object) - 库存记录对象

**record对象结构：**

```typescript
interface StockRecord {
  artName: string          // 品名
  artSpec: string          // 规格
  producer: string         // 生产厂家
  packCells: number        // 包装规格数量（每包含多少拆零单位）
  cellUnit: string         // 拆零单位
  packUnit: string         // 包装单位
  deptTotalPacks: number   // 仓库总库存整包数
  deptTotalCells: number   // 仓库总库存拆零数
  totalPacks: number       // 批次库存整包数
  totalCells: number       // 批次库存拆零数
  [key: string]: any       // 其他字段
}
```

### Events

组件内部处理所有事件，不对外暴露事件。

## 计算逻辑

### 双向联动计算

1. **整包数变化时：**
   - 剩余拆零数 = 总库存拆零数 - (整包数 × 包装规格)

2. **拆零数变化时：**
   - 对应整包数 = Math.floor((总库存拆零数 - 拆零数) / 包装规格)

### 数据校验

- 仓库总库存校验：输入的整包数×包装规格 + 拆零数 = 原始总库存
- 批次库存校验：输入的整包数×包装规格 + 拆零数 = 原始批次库存
- 批次整包数不能小于原始值
- 拆零数不能为负数

## 示例

查看 `examples/Basic.vue` 文件获取完整的使用示例。

## 打包发布

```bash
pnpm publish:component Wm/WholeBox
```

## 依赖

- Vue 3.x
- Ant Design Vue 4.x
- @idmy/core
- @mh-wm/util

## 许可证

MIT