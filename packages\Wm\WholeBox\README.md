# WholeBox 拆零盒整组件

拆零盒整组件是一个用于处理药品库存整包数与拆零数转换的Vue组件，支持仓库总库存和批次库存的双向联动计算。

## 功能特性

- ✅ **拆零盒整功能** - 支持整包数与拆零数的互相转换
- ✅ **双向联动计算** - 修改整包数自动计算拆零数，反之亦然
- ✅ **实时数据校验** - 确保输入数据的准确性和总量不变
- ✅ **API接口集成** - 调用后端接口进行数据持久化
- ✅ **用户友好界面** - 清晰的提示信息和操作反馈
- ✅ **TypeScript支持** - 完整的类型定义

## 安装

```bash
pnpm add @mh-wm/WholeBox
```

## 基本用法

```vue
<template>
  <div>
    <!-- 拆零盒整按钮 -->
    <a-button @click="handleSplitPack">拆零盒整</a-button>

    <!-- 拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WholeBox from '@mh-wm/WholeBox'

const wholeBoxRef = ref()

// 库存记录数据
const stockRecord = {
  artName: '阿莫西林胶囊',
  artSpec: '0.25g',
  producer: '华北制药',
  packCells: 10,        // 包装规格：10粒/盒
  cellUnit: '粒',       // 拆零单位
  packUnit: '盒',       // 包装单位
  deptTotalPacks: 5,    // 仓库总库存整包数
  deptTotalCells: 3,    // 仓库总库存拆零数
  totalPacks: 2,        // 批次库存整包数
  totalCells: 8,        // 批次库存拆零数
}

const handleSplitPack = () => {
  wholeBoxRef.value?.handleSplitPack(stockRecord)
}
</script>
```

## API

### Props

该组件不接受任何props，通过方法调用的方式使用。

### Methods

#### handleSplitPack(record)

打开拆零盒整录入模态框。

**参数：**

- `record` (Object) - 库存记录对象

**record对象结构：**

```typescript
interface StockRecord {
  artName: string          // 品名
  artSpec: string          // 规格
  producer: string         // 生产厂家
  packCells: number        // 包装规格数量（每包含多少拆零单位）
  cellUnit: string         // 拆零单位
  packUnit: string         // 包装单位
  deptTotalPacks: number   // 仓库总库存整包数
  deptTotalCells: number   // 仓库总库存拆零数
  totalPacks: number       // 批次库存整包数
  totalCells: number       // 批次库存拆零数
  [key: string]: any       // 其他字段
}
```

### Events

组件内部处理所有事件，不对外暴露事件。

## 计算逻辑

### 双向联动计算

1. **整包数变化时：**
   - 剩余拆零数 = 总库存拆零数 - (整包数 × 包装规格)

2. **拆零数变化时：**
   - 对应整包数 = Math.floor((总库存拆零数 - 拆零数) / 包装规格)

### 数据校验

- 仓库总库存校验：输入的整包数×包装规格 + 拆零数 = 原始总库存
- 批次库存校验：输入的整包数×包装规格 + 拆零数 = 原始批次库存
- 批次整包数不能小于原始值
- 拆零数不能为负数

## 示例

我们提供了丰富的示例来展示WholeBox组件在不同场景下的使用方法：

### 📚 示例列表

1. **基本用法** (`examples/Basic.vue`)
   - 组件的基本使用方法
   - 简单的拆零盒整操作
   - 代码示例和说明文档

2. **多场景应用** (`examples/MultipleScenarios.vue`)
   - 药品拆零盒整场景
   - 医疗器械拆零场景
   - 特殊包装规格处理
   - 边界情况测试

3. **表格集成** (`examples/TableDemo.vue`)
   - 在数据表格中集成组件
   - 库存管理系统演示
   - 搜索、筛选、分页功能
   - 批量操作支持

4. **API集成** (`examples/ApiIntegration.vue`)
   - API调用监控和日志
   - 错误处理演示
   - 性能监控分析
   - 并发操作测试

5. **示例索引** (`examples/index.vue`)
   - 所有示例的导航页面
   - 快速开始指南
   - 特性介绍和技术栈

### 🚀 运行示例

```bash
# 进入组件目录
cd packages/Wm/WholeBox

# 查看基本用法示例
# 在你的Vue项目中导入 examples/Basic.vue

# 查看所有示例
# 在你的Vue项目中导入 examples/index.vue
```

### 💡 示例特点

- **完整性**: 每个示例都包含完整的代码和详细说明
- **实用性**: 基于真实业务场景设计
- **渐进性**: 从基础到高级，循序渐进
- **交互性**: 可直接运行和测试的示例代码

## 打包与发布

### 打包组件

在开发完成后，需要打包组件以便发布和使用：

```bash
# 在项目根目录下执行打包命令
pnpm publish:component Wm/WholeBox
```

这个命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

### 仅构建不发布

如果只想构建组件而不发布到npm仓库：

```bash
# 仅构建ES模块
pnpm publish:component Wm/WholeBox --no-publish

# 或者在组件目录下执行
cd packages/Wm/WholeBox
pnpm run publish:build
```

### 发布不同版本

```bash
# 发布开发版本
pnpm publish:dev-component Wm/WholeBox    # @mh-wm/whole-box@1.0.0-dev

# 发布测试版本
pnpm publish:test-component Wm/WholeBox   # @mh-wm/whole-box@1.0.0-test

# 发布Beta版本
pnpm publish:beta-component Wm/WholeBox   # @mh-wm/whole-box@1.0.0-beta
```

### 在组件目录下直接执行

```bash
# 进入组件目录
cd packages/Wm/WholeBox

# 仅构建
pnpm run publish:build

# 构建并发布
pnpm run publish:component
```

## 依赖

- Vue 3.x
- Ant Design Vue 4.x
- @idmy/core
- @mh-wm/util

## 许可证

MIT