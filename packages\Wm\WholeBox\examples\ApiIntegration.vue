<template>
  <div class="api-demo-container">
    <h2>WholeBox API集成示例</h2>
    
    <!-- API状态监控 -->
    <div class="status-section">
      <h3>🔗 API状态监控</h3>
      <div class="status-grid">
        <div class="status-item">
          <div class="status-label">API连接状态</div>
          <Tag :color="apiStatus.connected ? 'green' : 'red'">
            {{ apiStatus.connected ? '已连接' : '未连接' }}
          </Tag>
        </div>
        <div class="status-item">
          <div class="status-label">最后调用时间</div>
          <span>{{ apiStatus.lastCall || '暂无' }}</span>
        </div>
        <div class="status-item">
          <div class="status-label">成功次数</div>
          <span class="success-count">{{ apiStatus.successCount }}</span>
        </div>
        <div class="status-item">
          <div class="status-label">失败次数</div>
          <span class="error-count">{{ apiStatus.errorCount }}</span>
        </div>
      </div>
    </div>

    <!-- API调用日志 -->
    <div class="log-section">
      <h3>📋 API调用日志</h3>
      <div class="log-controls">
        <Button @click="clearLogs" size="small">
          <DeleteOutlined />
          清空日志
        </Button>
        <Button @click="exportLogs" size="small">
          <DownloadOutlined />
          导出日志
        </Button>
        <Switch 
          v-model:checked="autoScroll" 
          size="small"
        />
        <span style="margin-left: 8px;">自动滚动</span>
      </div>
      
      <div class="log-container" ref="logContainer">
        <div 
          v-for="(log, index) in apiLogs" 
          :key="index"
          :class="['log-item', `log-${log.type}`]"
        >
          <div class="log-time">{{ log.timestamp }}</div>
          <div class="log-type">{{ log.type.toUpperCase() }}</div>
          <div class="log-message">{{ log.message }}</div>
          <div v-if="log.data" class="log-data">
            <details>
              <summary>查看数据</summary>
              <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
            </details>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试用例 -->
    <div class="test-section">
      <h3>🧪 API测试用例</h3>
      <div class="test-grid">
        <div class="test-item">
          <h4>正常拆零盒整</h4>
          <p>测试正常的拆零盒整操作流程</p>
          <Button 
            type="primary" 
            @click="testNormalSplitPack"
            :loading="testLoading.normal"
          >
            执行测试
          </Button>
        </div>
        
        <div class="test-item">
          <h4>边界值测试</h4>
          <p>测试零库存、大数值等边界情况</p>
          <Button 
            type="primary" 
            @click="testEdgeCases"
            :loading="testLoading.edge"
          >
            执行测试
          </Button>
        </div>
        
        <div class="test-item">
          <h4>错误处理测试</h4>
          <p>测试网络错误、数据异常等错误处理</p>
          <Button 
            type="primary" 
            @click="testErrorHandling"
            :loading="testLoading.error"
          >
            执行测试
          </Button>
        </div>
        
        <div class="test-item">
          <h4>并发操作测试</h4>
          <p>测试多个拆零盒整操作的并发处理</p>
          <Button 
            type="primary" 
            @click="testConcurrency"
            :loading="testLoading.concurrent"
          >
            执行测试
          </Button>
        </div>
      </div>
    </div>

    <!-- 性能监控 -->
    <div class="performance-section">
      <h3>⚡ 性能监控</h3>
      <div class="performance-grid">
        <div class="performance-item">
          <div class="performance-label">平均响应时间</div>
          <div class="performance-value">{{ performance.avgResponseTime }}ms</div>
        </div>
        <div class="performance-item">
          <div class="performance-label">最快响应时间</div>
          <div class="performance-value">{{ performance.minResponseTime }}ms</div>
        </div>
        <div class="performance-item">
          <div class="performance-label">最慢响应时间</div>
          <div class="performance-value">{{ performance.maxResponseTime }}ms</div>
        </div>
        <div class="performance-item">
          <div class="performance-label">成功率</div>
          <div class="performance-value">{{ performance.successRate }}%</div>
        </div>
      </div>
    </div>

    <!-- 实际操作演示 -->
    <div class="demo-section">
      <h3>🎯 实际操作演示</h3>
      <p>点击下方按钮体验真实的拆零盒整操作，所有API调用都会被记录在上方日志中</p>
      
      <div class="demo-buttons">
        <Button 
          type="primary" 
          size="large"
          @click="demoSplitPack"
        >
          演示拆零盒整
        </Button>
        <Button 
          size="large"
          @click="demoWithMockError"
        >
          演示错误处理
        </Button>
      </div>
    </div>

    <!-- 使用拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" @api-call="handleApiCall" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, computed } from 'vue'
import { Button, Tag, Switch, message } from 'ant-design-vue'
import { DeleteOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import WholeBox from '../src/index.vue'

const wholeBoxRef = ref()
const logContainer = ref()
const autoScroll = ref(true)

// API状态
const apiStatus = reactive({
  connected: true,
  lastCall: '',
  successCount: 0,
  errorCount: 0
})

// 测试加载状态
const testLoading = reactive({
  normal: false,
  edge: false,
  error: false,
  concurrent: false
})

// 性能数据
const performance = reactive({
  responseTimes: [] as number[],
  avgResponseTime: 0,
  minResponseTime: 0,
  maxResponseTime: 0,
  successRate: 100
})

// 计算性能指标
const updatePerformance = () => {
  if (performance.responseTimes.length === 0) return
  
  performance.avgResponseTime = Math.round(
    performance.responseTimes.reduce((a, b) => a + b, 0) / performance.responseTimes.length
  )
  performance.minResponseTime = Math.min(...performance.responseTimes)
  performance.maxResponseTime = Math.max(...performance.responseTimes)
  
  const total = apiStatus.successCount + apiStatus.errorCount
  performance.successRate = total > 0 ? Math.round((apiStatus.successCount / total) * 100) : 100
}

// API调用日志
const apiLogs = ref<Array<{
  timestamp: string
  type: 'info' | 'success' | 'error' | 'warning'
  message: string
  data?: any
}>>([])

// 添加日志
const addLog = (type: 'info' | 'success' | 'error' | 'warning', message: string, data?: any) => {
  const timestamp = new Date().toLocaleTimeString()
  apiLogs.value.push({
    timestamp,
    type,
    message,
    data
  })
  
  // 自动滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      if (logContainer.value) {
        logContainer.value.scrollTop = logContainer.value.scrollHeight
      }
    })
  }
}

// 处理API调用
const handleApiCall = (event: any) => {
  const { type, data, responseTime } = event
  
  apiStatus.lastCall = new Date().toLocaleTimeString()
  
  if (type === 'success') {
    apiStatus.successCount++
    performance.responseTimes.push(responseTime)
    addLog('success', `拆零盒整操作成功 (${responseTime}ms)`, data)
  } else if (type === 'error') {
    apiStatus.errorCount++
    addLog('error', `拆零盒整操作失败: ${data.message}`, data)
  }
  
  updatePerformance()
}

// 模拟API调用
const mockApiCall = async (shouldFail = false, delay = 500) => {
  const startTime = Date.now()
  
  addLog('info', '开始API调用...')
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const responseTime = Date.now() - startTime
      
      if (shouldFail) {
        const error = { message: '模拟网络错误', code: 500 }
        handleApiCall({ type: 'error', data: error, responseTime })
        reject(error)
      } else {
        const result = { success: true, message: '操作成功' }
        handleApiCall({ type: 'success', data: result, responseTime })
        resolve(result)
      }
    }, delay)
  })
}

// 测试用例
const testNormalSplitPack = async () => {
  testLoading.normal = true
  addLog('info', '开始正常拆零盒整测试')
  
  try {
    await mockApiCall(false, 800)
    message.success('正常拆零盒整测试通过')
  } catch (error) {
    message.error('正常拆零盒整测试失败')
  } finally {
    testLoading.normal = false
  }
}

const testEdgeCases = async () => {
  testLoading.edge = true
  addLog('info', '开始边界值测试')
  
  try {
    // 测试零库存
    await mockApiCall(false, 600)
    addLog('success', '零库存测试通过')
    
    // 测试大数值
    await mockApiCall(false, 700)
    addLog('success', '大数值测试通过')
    
    message.success('边界值测试全部通过')
  } catch (error) {
    message.error('边界值测试失败')
  } finally {
    testLoading.edge = false
  }
}

const testErrorHandling = async () => {
  testLoading.error = true
  addLog('info', '开始错误处理测试')
  
  try {
    await mockApiCall(true, 1000)
  } catch (error) {
    addLog('success', '错误处理测试通过 - 正确捕获了错误')
    message.success('错误处理测试通过')
  } finally {
    testLoading.error = false
  }
}

const testConcurrency = async () => {
  testLoading.concurrent = true
  addLog('info', '开始并发操作测试')
  
  try {
    const promises = [
      mockApiCall(false, 500),
      mockApiCall(false, 600),
      mockApiCall(false, 700)
    ]
    
    await Promise.all(promises)
    addLog('success', '并发操作测试通过')
    message.success('并发操作测试通过')
  } catch (error) {
    message.error('并发操作测试失败')
  } finally {
    testLoading.concurrent = false
  }
}

// 演示操作
const demoSplitPack = () => {
  const mockRecord = {
    artName: '演示药品',
    artSpec: '100mg',
    producer: '演示制药公司',
    packCells: 10,
    cellUnit: '片',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8,
    artId: 9999,
    batchNo: 'DEMO001',
    expDate: '20251201'
  }
  
  if (wholeBoxRef.value) {
    wholeBoxRef.value.handleSplitPack(mockRecord)
  }
}

const demoWithMockError = async () => {
  addLog('info', '演示错误处理...')
  try {
    await mockApiCall(true, 800)
  } catch (error) {
    // 错误已在mockApiCall中处理
  }
}

// 工具方法
const clearLogs = () => {
  apiLogs.value = []
  addLog('info', '日志已清空')
}

const exportLogs = () => {
  const logData = apiLogs.value.map(log => 
    `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logData], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `wholebox-api-logs-${Date.now()}.txt`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('日志导出成功')
}

// 初始化
addLog('info', 'WholeBox API集成示例已加载')
</script>

<style lang="less" scoped>
.api-demo-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.status-section,
.log-section,
.test-section,
.performance-section,
.demo-section {
  margin-bottom: 32px;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;

  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #1890ff;
    font-size: 18px;
  }
}

.status-grid,
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item,
.performance-item {
  padding: 12px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;

  .status-label,
  .performance-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
  }

  .performance-value {
    font-size: 18px;
    font-weight: 600;
    color: #1890ff;
  }

  .success-count {
    color: #52c41a;
    font-weight: 600;
  }

  .error-count {
    color: #ff4d4f;
    font-weight: 600;
  }
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.log-container {
  height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
}

.log-item {
  display: grid;
  grid-template-columns: 80px 60px 1fr;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;

  &:last-child {
    border-bottom: none;
  }

  .log-time {
    color: #666;
  }

  .log-type {
    font-weight: 600;
  }

  .log-data {
    grid-column: 1 / -1;
    margin-top: 8px;

    details {
      cursor: pointer;
    }

    pre {
      background-color: #f6f8fa;
      padding: 8px;
      border-radius: 4px;
      font-size: 11px;
      overflow-x: auto;
    }
  }

  &.log-info .log-type {
    color: #1890ff;
  }

  &.log-success .log-type {
    color: #52c41a;
  }

  &.log-error .log-type {
    color: #ff4d4f;
  }

  &.log-warning .log-type {
    color: #faad14;
  }
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.test-item {
  padding: 16px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;

  h4 {
    margin-top: 0;
    margin-bottom: 8px;
    color: #262626;
  }

  p {
    margin-bottom: 12px;
    color: #666;
    font-size: 14px;
  }

  .ant-btn {
    width: 100%;
  }
}

.demo-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 20px;
}
</style>
