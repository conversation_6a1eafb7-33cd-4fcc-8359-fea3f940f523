<template>
  <div class="example-container">
    <h2>拆零盒整组件示例</h2>
    
    <div class="example-section">
      <h3>基本用法</h3>
      <p>点击"拆零盒整"按钮打开拆零盒整录入模态框</p>
      
      <Button type="primary" @click="handleOpenSplitPack">
        拆零盒整
      </Button>
      
      <!-- 使用拆零盒整组件 -->
      <WholeBox ref="wholeBoxRef" />
    </div>

    <div class="example-section">
      <h3>模拟数据</h3>
      <p>以下是用于测试的模拟库存数据：</p>
      <pre>{{ JSON.stringify(mockRecord, null, 2) }}</pre>
    </div>

    <div class="example-section">
      <h3>功能说明</h3>
      <ul>
        <li><strong>拆零盒整功能：</strong>支持仓库总库存和批次库存的整包数与拆零数互相转换</li>
        <li><strong>双向联动：</strong>修改整包数时自动计算拆零数，修改拆零数时自动计算整包数</li>
        <li><strong>数据校验：</strong>实时校验输入数据的准确性，确保总量不变</li>
        <li><strong>API调用：</strong>集成后端接口，支持数据持久化</li>
        <li><strong>用户友好：</strong>提供清晰的提示信息和操作反馈</li>
      </ul>
    </div>

    <div class="example-section">
      <h3>使用方法</h3>
      <div class="code-block">
        <h4>1. 导入组件</h4>
        <pre><code>import WholeBox from '@mh-wm/WholeBox'</code></pre>
        
        <h4>2. 在模板中使用</h4>
        <pre><code>&lt;WholeBox ref="wholeBoxRef" /&gt;</code></pre>
        
        <h4>3. 调用拆零盒整方法</h4>
        <pre><code>// 传入库存记录数据
const record = {
  artName: '阿莫西林胶囊',
  artSpec: '0.25g',
  producer: '华北制药',
  packCells: 10,
  cellUnit: '粒',
  packUnit: '盒',
  deptTotalPacks: 5,
  deptTotalCells: 3,
  totalPacks: 2,
  totalCells: 8
}

// 调用拆零盒整方法
wholeBoxRef.value.handleSplitPack(record)</code></pre>
      </div>
    </div>

    <div class="example-section">
      <h3>打包发布</h3>
      <p>使用以下命令打包并发布组件：</p>
      <div class="code-block">
        <pre><code>pnpm publish:component Wm/WholeBox</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from 'ant-design-vue'
import WholeBox from '../src/index.vue'

const wholeBoxRef = ref()

// 模拟库存数据
const mockRecord = {
  artName: '阿莫西林胶囊',
  artSpec: '0.25g',
  producer: '华北制药集团有限责任公司',
  packCells: 10,        // 包装规格：10粒/盒
  cellUnit: '粒',       // 拆零单位
  packUnit: '盒',       // 包装单位
  deptTotalPacks: 5,    // 仓库总库存整包数：5盒
  deptTotalCells: 3,    // 仓库总库存拆零数：3粒
  totalPacks: 2,        // 批次库存整包数：2盒
  totalCells: 8,        // 批次库存拆零数：8粒
  // 其他必要字段
  artId: 1001,
  batchNo: '*********',
  expDate: '20251201',
  deptId: 101,
  orgId: 1
}

// 打开拆零盒整模态框
const handleOpenSplitPack = () => {
  if (wholeBoxRef.value) {
    wholeBoxRef.value.handleSplitPack(mockRecord)
  }
}
</script>

<style lang="less" scoped>
.example-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 32px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;

  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #1890ff;
  }

  p {
    margin-bottom: 16px;
    line-height: 1.6;
  }

  ul {
    margin-bottom: 16px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.6;
      
      strong {
        color: #1890ff;
      }
    }
  }
}

.code-block {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;

  h4 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #24292e;
    font-size: 14px;
    font-weight: 600;
  }

  pre {
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 12px;
    line-height: 1.45;
    color: #24292e;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  code {
    background: transparent;
    border: none;
    padding: 0;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
  }
}

pre {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  line-height: 1.45;
  color: #24292e;
  overflow-x: auto;
}
</style>
