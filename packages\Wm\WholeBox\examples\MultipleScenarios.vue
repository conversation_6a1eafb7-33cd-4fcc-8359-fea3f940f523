<template>
  <div class="scenarios-container">
    <h2>WholeBox 多场景示例</h2>
    
    <!-- 场景1: 药品拆零盒整 -->
    <div class="scenario-section">
      <h3>🏥 场景1: 药品拆零盒整</h3>
      <p>常见的药品库存拆零盒整操作</p>
      
      <div class="demo-grid">
        <div class="demo-item">
          <h4>阿莫西林胶囊</h4>
          <p>规格: 0.25g × 10粒/盒</p>
          <p>仓库库存: 5盒3粒 → 批次库存: 2盒8粒</p>
          <Button type="primary" @click="() => handleSplitPack(medicineRecord1)">
            拆零盒整
          </Button>
        </div>
        
        <div class="demo-item">
          <h4>头孢克肟片</h4>
          <p>规格: 100mg × 6片/盒</p>
          <p>仓库库存: 8盒2片 → 批次库存: 3盒4片</p>
          <Button type="primary" @click="() => handleSplitPack(medicineRecord2)">
            拆零盒整
          </Button>
        </div>
        
        <div class="demo-item">
          <h4>布洛芬缓释胶囊</h4>
          <p>规格: 300mg × 20粒/盒</p>
          <p>仓库库存: 12盒15粒 → 批次库存: 5盒18粒</p>
          <Button type="primary" @click="() => handleSplitPack(medicineRecord3)">
            拆零盒整
          </Button>
        </div>
      </div>
    </div>

    <!-- 场景2: 医疗器械拆零盒整 -->
    <div class="scenario-section">
      <h3>🔬 场景2: 医疗器械拆零盒整</h3>
      <p>医疗器械的包装拆零操作</p>
      
      <div class="demo-grid">
        <div class="demo-item">
          <h4>一次性注射器</h4>
          <p>规格: 5ml × 100支/盒</p>
          <p>仓库库存: 3盒25支 → 批次库存: 1盒50支</p>
          <Button type="primary" @click="() => handleSplitPack(deviceRecord1)">
            拆零盒整
          </Button>
        </div>
        
        <div class="demo-item">
          <h4>医用口罩</h4>
          <p>规格: 50只/盒</p>
          <p>仓库库存: 10盒30只 → 批次库存: 4盒20只</p>
          <Button type="primary" @click="() => handleSplitPack(deviceRecord2)">
            拆零盒整
          </Button>
        </div>
      </div>
    </div>

    <!-- 场景3: 特殊包装规格 -->
    <div class="scenario-section">
      <h3>📦 场景3: 特殊包装规格</h3>
      <p>不同包装规格的拆零盒整处理</p>
      
      <div class="demo-grid">
        <div class="demo-item">
          <h4>胰岛素笔芯</h4>
          <p>规格: 3ml × 5支/盒</p>
          <p>仓库库存: 2盒3支 → 批次库存: 1盒2支</p>
          <Button type="primary" @click="() => handleSplitPack(specialRecord1)">
            拆零盒整
          </Button>
        </div>
        
        <div class="demo-item">
          <h4>维生素C泡腾片</h4>
          <p>规格: 1g × 12片/筒</p>
          <p>仓库库存: 6筒8片 → 批次库存: 2筒10片</p>
          <Button type="primary" @click="() => handleSplitPack(specialRecord2)">
            拆零盒整
          </Button>
        </div>
        
        <div class="demo-item">
          <h4>眼药水</h4>
          <p>规格: 5ml × 1瓶/盒</p>
          <p>仓库库存: 15盒0瓶 → 批次库存: 8盒0瓶</p>
          <Button type="primary" @click="() => handleSplitPack(specialRecord3)">
            拆零盒整
          </Button>
        </div>
      </div>
    </div>

    <!-- 场景4: 边界情况测试 -->
    <div class="scenario-section">
      <h3>⚠️ 场景4: 边界情况测试</h3>
      <p>测试各种边界情况和异常处理</p>
      
      <div class="demo-grid">
        <div class="demo-item">
          <h4>零库存情况</h4>
          <p>仓库库存: 0盒0粒 → 批次库存: 0盒0粒</p>
          <Button type="primary" @click="() => handleSplitPack(edgeRecord1)">
            测试零库存
          </Button>
        </div>
        
        <div class="demo-item">
          <h4>大包装规格</h4>
          <p>规格: 500片/瓶</p>
          <p>仓库库存: 2瓶350片 → 批次库存: 1瓶200片</p>
          <Button type="primary" @click="() => handleSplitPack(edgeRecord2)">
            测试大包装
          </Button>
        </div>
        
        <div class="demo-item">
          <h4>单包装规格</h4>
          <p>规格: 1支/盒</p>
          <p>仓库库存: 10盒0支 → 批次库存: 5盒0支</p>
          <Button type="primary" @click="() => handleSplitPack(edgeRecord3)">
            测试单包装
          </Button>
        </div>
      </div>
    </div>

    <!-- 使用拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from 'ant-design-vue'
import WholeBox from '../src/index.vue'

const wholeBoxRef = ref()

// 药品示例数据
const medicineRecord1 = {
  artName: '阿莫西林胶囊',
  artSpec: '0.25g',
  producer: '华北制药集团有限责任公司',
  packCells: 10,
  cellUnit: '粒',
  packUnit: '盒',
  deptTotalPacks: 5,
  deptTotalCells: 3,
  totalPacks: 2,
  totalCells: 8,
  artId: 1001,
  batchNo: '*********',
  expDate: '20251201'
}

const medicineRecord2 = {
  artName: '头孢克肟片',
  artSpec: '100mg',
  producer: '齐鲁制药有限公司',
  packCells: 6,
  cellUnit: '片',
  packUnit: '盒',
  deptTotalPacks: 8,
  deptTotalCells: 2,
  totalPacks: 3,
  totalCells: 4,
  artId: 1002,
  batchNo: '*********',
  expDate: '20251202'
}

const medicineRecord3 = {
  artName: '布洛芬缓释胶囊',
  artSpec: '300mg',
  producer: '中美史克制药有限公司',
  packCells: 20,
  cellUnit: '粒',
  packUnit: '盒',
  deptTotalPacks: 12,
  deptTotalCells: 15,
  totalPacks: 5,
  totalCells: 18,
  artId: 1003,
  batchNo: 'B20240103',
  expDate: '20251203'
}

// 医疗器械示例数据
const deviceRecord1 = {
  artName: '一次性注射器',
  artSpec: '5ml',
  producer: '山东威高集团医用高分子制品股份有限公司',
  packCells: 100,
  cellUnit: '支',
  packUnit: '盒',
  deptTotalPacks: 3,
  deptTotalCells: 25,
  totalPacks: 1,
  totalCells: 50,
  artId: 2001,
  batchNo: 'D20240101',
  expDate: '20261201'
}

const deviceRecord2 = {
  artName: '医用口罩',
  artSpec: '一次性',
  producer: '3M中国有限公司',
  packCells: 50,
  cellUnit: '只',
  packUnit: '盒',
  deptTotalPacks: 10,
  deptTotalCells: 30,
  totalPacks: 4,
  totalCells: 20,
  artId: 2002,
  batchNo: 'D20240102',
  expDate: '20261202'
}

// 特殊包装示例数据
const specialRecord1 = {
  artName: '胰岛素笔芯',
  artSpec: '3ml',
  producer: '诺和诺德(中国)制药有限公司',
  packCells: 5,
  cellUnit: '支',
  packUnit: '盒',
  deptTotalPacks: 2,
  deptTotalCells: 3,
  totalPacks: 1,
  totalCells: 2,
  artId: 3001,
  batchNo: 'S20240101',
  expDate: '20251201'
}

const specialRecord2 = {
  artName: '维生素C泡腾片',
  artSpec: '1g',
  producer: '拜耳医药保健有限公司',
  packCells: 12,
  cellUnit: '片',
  packUnit: '筒',
  deptTotalPacks: 6,
  deptTotalCells: 8,
  totalPacks: 2,
  totalCells: 10,
  artId: 3002,
  batchNo: 'S20240102',
  expDate: '20251202'
}

const specialRecord3 = {
  artName: '玻璃酸钠滴眼液',
  artSpec: '5ml',
  producer: '参天制药(中国)有限公司',
  packCells: 1,
  cellUnit: '瓶',
  packUnit: '盒',
  deptTotalPacks: 15,
  deptTotalCells: 0,
  totalPacks: 8,
  totalCells: 0,
  artId: 3003,
  batchNo: 'S20240103',
  expDate: '20251203'
}

// 边界情况示例数据
const edgeRecord1 = {
  artName: '测试药品(零库存)',
  artSpec: '100mg',
  producer: '测试制药公司',
  packCells: 10,
  cellUnit: '粒',
  packUnit: '盒',
  deptTotalPacks: 0,
  deptTotalCells: 0,
  totalPacks: 0,
  totalCells: 0,
  artId: 9001,
  batchNo: 'E20240101',
  expDate: '20251201'
}

const edgeRecord2 = {
  artName: '大包装测试药品',
  artSpec: '500mg',
  producer: '测试制药公司',
  packCells: 500,
  cellUnit: '片',
  packUnit: '瓶',
  deptTotalPacks: 2,
  deptTotalCells: 350,
  totalPacks: 1,
  totalCells: 200,
  artId: 9002,
  batchNo: '*********',
  expDate: '20251202'
}

const edgeRecord3 = {
  artName: '单包装测试药品',
  artSpec: '1ml',
  producer: '测试制药公司',
  packCells: 1,
  cellUnit: '支',
  packUnit: '盒',
  deptTotalPacks: 10,
  deptTotalCells: 0,
  totalPacks: 5,
  totalCells: 0,
  artId: 9003,
  batchNo: '*********',
  expDate: '20251203'
}

// 打开拆零盒整模态框
const handleSplitPack = (record: any) => {
  if (wholeBoxRef.value) {
    wholeBoxRef.value.handleSplitPack(record)
  }
}
</script>

<style lang="less" scoped>
.scenarios-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.scenario-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;

  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #1890ff;
    font-size: 18px;
  }

  p {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.6;
  }
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.demo-item {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }

  h4 {
    margin-top: 0;
    margin-bottom: 8px;
    color: #262626;
    font-size: 16px;
    font-weight: 600;
  }

  p {
    margin-bottom: 8px;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
  }

  .ant-btn {
    margin-top: 12px;
    width: 100%;
  }
}
</style>
