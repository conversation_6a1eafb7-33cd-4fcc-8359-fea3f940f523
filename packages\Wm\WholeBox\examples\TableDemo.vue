<template>
  <div class="table-demo-container">
    <h2>WholeBox 表格集成示例</h2>
    
    <div class="demo-section">
      <h3>库存管理表格</h3>
      <p>在表格中集成拆零盒整功能，模拟真实的库存管理场景</p>
      
      <!-- 操作工具栏 -->
      <div class="toolbar">
        <Button type="primary" @click="refreshData">
          <ReloadOutlined />
          刷新数据
        </Button>
        <Button @click="exportData">
          <ExportOutlined />
          导出数据
        </Button>
        <div class="search-box">
          <Input.Search
            v-model:value="searchText"
            placeholder="搜索药品名称或规格"
            style="width: 300px"
            @search="handleSearch"
          />
        </div>
      </div>

      <!-- 库存数据表格 -->
      <Table
        :columns="columns"
        :data-source="filteredData"
        :pagination="pagination"
        :loading="loading"
        row-key="artId"
        size="middle"
        bordered
      >
        <!-- 药品信息列 -->
        <template #artInfo="{ record }">
          <div class="art-info">
            <div class="art-name">{{ record.artName }}</div>
            <div class="art-spec">{{ record.artSpec }}</div>
            <div class="art-producer">{{ record.producer }}</div>
          </div>
        </template>

        <!-- 包装规格列 -->
        <template #packSpec="{ record }">
          <Tag color="blue">
            {{ record.packCells }}{{ record.cellUnit }}/{{ record.packUnit }}
          </Tag>
        </template>

        <!-- 仓库总库存列 -->
        <template #deptStock="{ record }">
          <div class="stock-info">
            <div class="stock-packs">
              <strong>{{ record.deptTotalPacks }}</strong>{{ record.packUnit }}
            </div>
            <div class="stock-cells" v-if="record.deptTotalCells > 0">
              <strong>{{ record.deptTotalCells }}</strong>{{ record.cellUnit }}
            </div>
          </div>
        </template>

        <!-- 批次库存列 -->
        <template #batchStock="{ record }">
          <div class="stock-info">
            <div class="stock-packs">
              <strong>{{ record.totalPacks }}</strong>{{ record.packUnit }}
            </div>
            <div class="stock-cells" v-if="record.totalCells > 0">
              <strong>{{ record.totalCells }}</strong>{{ record.cellUnit }}
            </div>
          </div>
        </template>

        <!-- 批次信息列 -->
        <template #batchInfo="{ record }">
          <div class="batch-info">
            <div class="batch-no">批次: {{ record.batchNo }}</div>
            <div class="exp-date">效期: {{ formatDate(record.expDate) }}</div>
          </div>
        </template>

        <!-- 库存状态列 -->
        <template #stockStatus="{ record }">
          <Tag :color="getStockStatusColor(record)">
            {{ getStockStatusText(record) }}
          </Tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <Space>
            <Button 
              type="primary" 
              size="small"
              @click="handleSplitPack(record)"
            >
              拆零盒整
            </Button>
            <Button 
              size="small"
              @click="viewDetails(record)"
            >
              查看详情
            </Button>
          </Space>
        </template>
      </Table>
    </div>

    <!-- 使用拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />

    <!-- 详情模态框 -->
    <Modal
      v-model:open="detailModalVisible"
      title="库存详情"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedRecord" class="detail-content">
        <Descriptions :column="2" bordered>
          <Descriptions.Item label="药品名称">{{ selectedRecord.artName }}</Descriptions.Item>
          <Descriptions.Item label="规格">{{ selectedRecord.artSpec }}</Descriptions.Item>
          <Descriptions.Item label="生产厂家">{{ selectedRecord.producer }}</Descriptions.Item>
          <Descriptions.Item label="包装规格">
            {{ selectedRecord.packCells }}{{ selectedRecord.cellUnit }}/{{ selectedRecord.packUnit }}
          </Descriptions.Item>
          <Descriptions.Item label="仓库总库存">
            {{ selectedRecord.deptTotalPacks }}{{ selectedRecord.packUnit }}
            {{ selectedRecord.deptTotalCells > 0 ? selectedRecord.deptTotalCells + selectedRecord.cellUnit : '' }}
          </Descriptions.Item>
          <Descriptions.Item label="批次库存">
            {{ selectedRecord.totalPacks }}{{ selectedRecord.packUnit }}
            {{ selectedRecord.totalCells > 0 ? selectedRecord.totalCells + selectedRecord.cellUnit : '' }}
          </Descriptions.Item>
          <Descriptions.Item label="批次号">{{ selectedRecord.batchNo }}</Descriptions.Item>
          <Descriptions.Item label="有效期">{{ formatDate(selectedRecord.expDate) }}</Descriptions.Item>
        </Descriptions>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  Table, 
  Button, 
  Input, 
  Tag, 
  Space, 
  Modal, 
  Descriptions,
  message 
} from 'ant-design-vue'
import { ReloadOutlined, ExportOutlined } from '@ant-design/icons-vue'
import WholeBox from '../src/index.vue'

const wholeBoxRef = ref()
const loading = ref(false)
const searchText = ref('')
const detailModalVisible = ref(false)
const selectedRecord = ref(null)

// 表格列配置
const columns = [
  {
    title: '药品信息',
    key: 'artInfo',
    width: 200,
    slots: { customRender: 'artInfo' }
  },
  {
    title: '包装规格',
    key: 'packSpec',
    width: 120,
    align: 'center',
    slots: { customRender: 'packSpec' }
  },
  {
    title: '仓库总库存',
    key: 'deptStock',
    width: 120,
    align: 'center',
    slots: { customRender: 'deptStock' }
  },
  {
    title: '批次库存',
    key: 'batchStock',
    width: 120,
    align: 'center',
    slots: { customRender: 'batchStock' }
  },
  {
    title: '批次信息',
    key: 'batchInfo',
    width: 150,
    slots: { customRender: 'batchInfo' }
  },
  {
    title: '库存状态',
    key: 'stockStatus',
    width: 100,
    align: 'center',
    slots: { customRender: 'stockStatus' }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 分页配置
const pagination = {
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 模拟库存数据
const stockData = ref([
  {
    artId: 1001,
    artName: '阿莫西林胶囊',
    artSpec: '0.25g',
    producer: '华北制药集团有限责任公司',
    packCells: 10,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 15,
    deptTotalCells: 8,
    totalPacks: 5,
    totalCells: 3,
    batchNo: 'B20240101',
    expDate: '20251201'
  },
  {
    artId: 1002,
    artName: '头孢克肟片',
    artSpec: '100mg',
    producer: '齐鲁制药有限公司',
    packCells: 6,
    cellUnit: '片',
    packUnit: '盒',
    deptTotalPacks: 8,
    deptTotalCells: 2,
    totalPacks: 3,
    totalCells: 4,
    batchNo: 'B20240102',
    expDate: '20251202'
  },
  {
    artId: 1003,
    artName: '布洛芬缓释胶囊',
    artSpec: '300mg',
    producer: '中美史克制药有限公司',
    packCells: 20,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 12,
    deptTotalCells: 15,
    totalPacks: 5,
    totalCells: 18,
    batchNo: 'B20240103',
    expDate: '20251203'
  },
  {
    artId: 2001,
    artName: '一次性注射器',
    artSpec: '5ml',
    producer: '山东威高集团医用高分子制品股份有限公司',
    packCells: 100,
    cellUnit: '支',
    packUnit: '盒',
    deptTotalPacks: 3,
    deptTotalCells: 25,
    totalPacks: 1,
    totalCells: 50,
    batchNo: 'D20240101',
    expDate: '20261201'
  },
  {
    artId: 3001,
    artName: '胰岛素笔芯',
    artSpec: '3ml',
    producer: '诺和诺德(中国)制药有限公司',
    packCells: 5,
    cellUnit: '支',
    packUnit: '盒',
    deptTotalPacks: 2,
    deptTotalCells: 3,
    totalPacks: 1,
    totalCells: 2,
    batchNo: 'S20240101',
    expDate: '20251201'
  }
])

// 过滤后的数据
const filteredData = computed(() => {
  if (!searchText.value) {
    return stockData.value
  }
  
  return stockData.value.filter(item => 
    item.artName.toLowerCase().includes(searchText.value.toLowerCase()) ||
    item.artSpec.toLowerCase().includes(searchText.value.toLowerCase()) ||
    item.producer.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr || dateStr.length !== 8) return dateStr
  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
}

// 获取库存状态颜色
const getStockStatusColor = (record: any) => {
  const totalStock = record.deptTotalPacks * record.packCells + record.deptTotalCells
  if (totalStock === 0) return 'red'
  if (totalStock < record.packCells * 2) return 'orange'
  return 'green'
}

// 获取库存状态文本
const getStockStatusText = (record: any) => {
  const totalStock = record.deptTotalPacks * record.packCells + record.deptTotalCells
  if (totalStock === 0) return '缺货'
  if (totalStock < record.packCells * 2) return '库存不足'
  return '库存充足'
}

// 搜索处理
const handleSearch = (value: string) => {
  searchText.value = value
}

// 刷新数据
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据刷新成功')
  }, 1000)
}

// 导出数据
const exportData = () => {
  message.info('导出功能开发中...')
}

// 拆零盒整操作
const handleSplitPack = (record: any) => {
  if (wholeBoxRef.value) {
    wholeBoxRef.value.handleSplitPack(record)
  }
}

// 查看详情
const viewDetails = (record: any) => {
  selectedRecord.value = record
  detailModalVisible.value = true
}

onMounted(() => {
  pagination.total = stockData.value.length
})
</script>

<style lang="less" scoped>
.table-demo-container {
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 32px;

  h3 {
    margin-bottom: 16px;
    color: #1890ff;
    font-size: 18px;
  }

  p {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.6;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;

  .search-box {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }

  .ant-btn {
    margin-right: 8px;
  }
}

.art-info {
  .art-name {
    font-weight: 600;
    color: #262626;
    margin-bottom: 4px;
  }

  .art-spec {
    color: #666;
    font-size: 12px;
    margin-bottom: 2px;
  }

  .art-producer {
    color: #999;
    font-size: 11px;
  }
}

.stock-info {
  .stock-packs {
    margin-bottom: 2px;
  }

  .stock-cells {
    color: #666;
    font-size: 12px;
  }
}

.batch-info {
  .batch-no {
    font-size: 12px;
    margin-bottom: 2px;
  }

  .exp-date {
    color: #666;
    font-size: 11px;
  }
}

.detail-content {
  padding: 16px 0;
}

:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}
</style>
