<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Button, Col, Form, FormItem, Input, InputNumber, Popover, RangePicker, Row, Tag, Tooltip, Modal, Divider, Alert, message } from 'ant-design-vue'
import { http } from '@idmy/core'

// 拆零盒整API
const splitPackApi = (params: any) => {
  return http.post('/clinics_wm/wmbill/performSplitPack', params, { appKey: 'wm' })
}

// 拆零盒整相关状态
const splitPackModalVisible = ref(false)
const currentSplitRecord = ref<any>(null)
const splitPackForm = reactive({
  deptMaxPacks: 0,    // 仓库总库存能合并的最大包装数
  batchMaxPacks: 0,   // 批次库存能合并的最大包装数
  deptInputPacks: 0,  // 用户输入的仓库整包数
  batchInputPacks: 0, // 用户输入的批次整包数
  deptInputCells: 0,  // 用户输入的仓库拆零数
  batchInputCells: 0, // 用户输入的批次拆零数
  deptMaxCells: 0,    // 仓库总库存最大拆零数
  batchMaxCells: 0,   // 批次库存最大拆零数
})

const recodedate = ref()

// 计算仓库总库存最大拆零数
const getDeptMaxCells = () => {
  if (!currentSplitRecord.value) return 0
  const packCells = currentSplitRecord.value.packCells || 1
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  return deptTotalPacks * packCells + deptTotalCells
}

// 计算批次库存最大拆零数
const getBatchMaxCells = () => {
  if (!currentSplitRecord.value) return 0
  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  return batchTotalPacks * packCells + batchTotalCells
}

// 计算能合并的最大包装数和拆零数
const calculateMaxPackCount = (record: any) => {
  const packCells = record.packCells || 1

  // 计算仓库总库存
  const deptTotalPacks = record.deptTotalPacks || 0
  const deptTotalCells = record.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells

  // 仓库总库存的最大包装数和剩余拆零数
  splitPackForm.deptMaxPacks = Math.floor(deptTotalInCells / packCells)
  splitPackForm.deptMaxCells = deptTotalInCells % packCells

  // 计算批次库存
  const batchTotalPacks = record.totalPacks || 0
  const batchTotalCells = record.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells

  // 批次库存的最大包装数和剩余拆零数
  splitPackForm.batchMaxPacks = Math.floor(batchTotalInCells / packCells)
  splitPackForm.batchMaxCells = batchTotalInCells % packCells

  // 初始化输入值为最大值
  splitPackForm.deptInputPacks = splitPackForm.deptMaxPacks
  splitPackForm.deptInputCells = splitPackForm.deptMaxCells
  splitPackForm.batchInputPacks = splitPackForm.batchMaxPacks
  splitPackForm.batchInputCells = splitPackForm.batchMaxCells
}

// 仓库总库存整包数变化时的联动计算
const handleDeptPacksChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells

  // 计算剩余拆零数
  const usedCells = Number(value) * packCells
  const remainingCells = deptTotalInCells - usedCells

  if (remainingCells >= 0) {
    splitPackForm.deptInputCells = remainingCells
  }
}

// 仓库总库存拆零数变化时的联动计算
const handleDeptCellsChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells
  const numValue = Number(value)

  // 验证拆零数不能超过原始总库存
  if (numValue > deptTotalInCells) {
    message.warning('拆零数不能超过原始总库存')
    splitPackForm.deptInputCells = deptTotalInCells
    splitPackForm.deptInputPacks = 0
    return
  }

  // 验证拆零数不能为负数
  if (numValue < 0) {
    splitPackForm.deptInputCells = 0
    splitPackForm.deptInputPacks = Math.floor(deptTotalInCells / packCells)
    return
  }

  // 计算对应的整包数
  const correspondingPacks = Math.floor((deptTotalInCells - numValue) / packCells)
  splitPackForm.deptInputPacks = correspondingPacks
}

// 批次库存整包数变化时的联动计算
const handleBatchPacksChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells

  // 计算剩余拆零数
  const usedCells = Number(value) * packCells
  const remainingCells = batchTotalInCells - usedCells

  if (remainingCells >= 0) {
    splitPackForm.batchInputCells = remainingCells
  }
}

// 批次库存拆零数变化时的联动计算
const handleBatchCellsChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells
  const numValue = Number(value)

  // 验证拆零数不能超过原始总库存
  if (numValue > batchTotalInCells) {
    message.warning('拆零数不能超过原始总库存')
    splitPackForm.batchInputCells = batchTotalInCells
    splitPackForm.batchInputPacks = 0
    return
  }

  // 验证拆零数不能为负数
  if (numValue < 0) {
    splitPackForm.batchInputCells = 0
    splitPackForm.batchInputPacks = Math.floor(batchTotalInCells / packCells)
    return
  }

  // 计算对应的整包数
  const correspondingPacks = Math.floor((batchTotalInCells - numValue) / packCells)
  splitPackForm.batchInputPacks = correspondingPacks
}

// 拆零盒整方法
const handleSplitPack = (record: any) => {
  // 打开拆零盒整录入页面
  splitPackModalVisible.value = true
  currentSplitRecord.value = record
  recodedate.value = record
  // 计算能合并的最大包装数
  calculateMaxPackCount(record)
}

// 校验整包数与拆零数的准确性
const validateSplitPackData = () => {
  if (!currentSplitRecord.value) {
    message.error('数据异常，请重新操作')
    return false
  }

  // 校验批次库存整包数不能小于原始值
  const originalBatchPacks = currentSplitRecord.value.totalPacks || 0
  if (splitPackForm.batchInputPacks < originalBatchPacks) {
    message.error(`批次库存整包数不能小于原始值(${originalBatchPacks}${currentSplitRecord.value.packUnit})`)
    return false
  }

  // 校验拆零数不能为负数
  if (splitPackForm.deptInputCells < 0 || splitPackForm.batchInputCells < 0) {
    message.error('拆零数不能为负数')
    return false
  }

  // 注释：允许拆零数大于包装规格
  // 例如：包装规格10片/盒，允许输入15片、25片等
  // 系统会自动处理转换逻辑

  return true
}

// 获取校验状态
const getValidationStatus = () => {
  if (!currentSplitRecord.value) {
    return { isValid: false, message: '数据异常' }
  }

  const packCells = currentSplitRecord.value.packCells || 1

  // 计算仓库总库存原始总量（以拆零单位计算）
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  const deptOriginalTotal = deptTotalPacks * packCells + deptTotalCells

  // 计算用户输入的仓库总库存总量（以拆零单位计算）
  const deptInputTotal = splitPackForm.deptInputPacks * packCells + splitPackForm.deptInputCells

  // 校验仓库总库存是否匹配
  const deptMatched = deptInputTotal === deptOriginalTotal

  // 计算批次库存原始总量（以拆零单位计算）
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  const batchOriginalTotal = batchTotalPacks * packCells + batchTotalCells

  // 计算用户输入的批次库存总量（以拆零单位计算）
  const batchInputTotal = splitPackForm.batchInputPacks * packCells + splitPackForm.batchInputCells

  // 校验批次库存是否匹配
  const batchMatched = batchInputTotal === batchOriginalTotal

  if (!deptMatched) {
    return {
      isValid: false,
      message: `仓库总库存不匹配：输入${deptInputTotal}${currentSplitRecord.value.cellUnit}，应为${deptOriginalTotal}${currentSplitRecord.value.cellUnit}`
    }
  }

  if (!batchMatched) {
    return {
      isValid: false,
      message: `批次库存不匹配：输入${batchInputTotal}${currentSplitRecord.value.cellUnit}，应为${batchOriginalTotal}${currentSplitRecord.value.cellUnit}`
    }
  }

  return { isValid: true, message: '数据校验通过' }
}

// 确认拆零盒整操作
const handleSplitPackConfirm = () => {
  if (!currentSplitRecord.value) return

  // 校验数据准确性
  if (!validateSplitPackData()) {
    return
  }

  // 执行拆零盒整操作
  performSplitPackWithInput(currentSplitRecord.value)

  // 关闭模态框
  splitPackModalVisible.value = false
}

// 根据用户输入执行拆零盒整操作
const performSplitPackWithInput = async (record: any) => {
  try {
    // 准备传递给后台的数据
    const requestData = {
      ...record,
      // 更新后的仓库总库存
      deptTotalPacks: splitPackForm.deptInputPacks,
      deptTotalCells: splitPackForm.deptInputCells,
      // 更新后的批次库存
      totalPacks: splitPackForm.batchInputPacks,
      totalCells: splitPackForm.batchInputCells,
    }

    console.log('拆零盒整操作开始，传递数据:', JSON.stringify(requestData, null, 2))
    console.log('recodedate数据:', JSON.stringify(recodedate.value, null, 2))

    // 调用后台接口，将recodedate通过JSON格式传递
    const response = await splitPackApi({
      record: JSON.stringify(requestData),
      recodedate: JSON.stringify(recodedate.value)
    })

    if (response && (response as any).data && (response as any).data.success) {
      // 显示成功信息
      const deptDisplay = splitPackForm.deptInputPacks > 0 ?
        `${splitPackForm.deptInputPacks}${record.packUnit}` +
        (splitPackForm.deptInputCells > 0 ? `${splitPackForm.deptInputCells}${record.cellUnit}` : '') :
        (splitPackForm.deptInputCells > 0 ? `${splitPackForm.deptInputCells}${record.cellUnit}` : '0')

      const batchDisplay = splitPackForm.batchInputPacks > 0 ?
        `${splitPackForm.batchInputPacks}${record.packUnit}` +
        (splitPackForm.batchInputCells > 0 ? `${splitPackForm.batchInputCells}${record.cellUnit}` : '') :
        (splitPackForm.batchInputCells > 0 ? `${splitPackForm.batchInputCells}${record.cellUnit}` : '0')

      message.success(`拆零盒整完成！
仓库总库存：${deptDisplay}
批次库存：${batchDisplay}`)
    }
  } catch (error) {
    console.error('拆零盒整操作失败:', error)
    message.error('拆零盒整操作失败，请重试')
  }
}

// 取消拆零盒整操作
const handleSplitPackCancel = () => {
  splitPackModalVisible.value = false
  currentSplitRecord.value = null
}

// 暴露方法供外部调用
defineExpose({
  handleSplitPack
})
</script>

<template>
  <!-- 拆零盒整录入模态框 -->
  <a-modal
    v-model:open="splitPackModalVisible"
    title="拆零盒整录入"
    width="600px"
    :ok-button-props="{ disabled: !getValidationStatus().isValid }"
    @ok="handleSplitPackConfirm"
    @cancel="handleSplitPackCancel"
  >
    <div v-if="currentSplitRecord">
      <div class="mb-16px">
        <p><strong>品名：</strong>{{ currentSplitRecord.artName }}</p>
        <p><strong>规格：</strong>{{ currentSplitRecord.artSpec }}</p>
        <p><strong>生产厂家：</strong>{{ currentSplitRecord.producer }}</p>
        <p><strong>包装规格：</strong>{{ currentSplitRecord.packCells }}{{ currentSplitRecord.cellUnit }}/{{ currentSplitRecord.packUnit }}</p>
      </div>

      <a-divider />

      <a-form :model="splitPackForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="仓库总库存整包数">
              <a-input-number
                v-model:value="splitPackForm.deptInputPacks"
                :min="0"
                :max="splitPackForm.deptMaxPacks"
                :addon-after="currentSplitRecord.packUnit"
                :disabled="true"
                style="width: 100%"
                @change="handleDeptPacksChange"
              />
              <div class="text-gray-500 text-sm mt-1">
                (原始：{{ currentSplitRecord.deptTotalPacks }}{{ currentSplitRecord.packUnit }})
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="仓库总库存拆零数">
              <a-input-number
                v-model:value="splitPackForm.deptInputCells"
                :min="0"
                :max="getDeptMaxCells()"
                :addon-after="currentSplitRecord.cellUnit"
                :disabled="true"
                style="width: 100%"
                @change="handleDeptCellsChange"
              />
              <div class="text-gray-500 text-sm mt-1">
                (原始：{{ currentSplitRecord.deptTotalCells || 0 }}{{ currentSplitRecord.cellUnit }})
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="批次库存整包数">
              <a-input-number
                v-model:value="splitPackForm.batchInputPacks"
                :min="currentSplitRecord.totalPacks || 0"
                :max="Math.floor(getBatchMaxCells() / (currentSplitRecord.packCells || 1))"
                :addon-after="currentSplitRecord.packUnit"
                style="width: 100%"
                @change="handleBatchPacksChange"
              />
              <div class="text-gray-500 text-sm mt-1">
                范围：{{ currentSplitRecord.totalPacks || 0 }}-{{ Math.floor(getBatchMaxCells() / (currentSplitRecord.packCells || 1)) }}{{ currentSplitRecord.packUnit }}
                (原始：{{ currentSplitRecord.totalPacks }}{{ currentSplitRecord.packUnit }})
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="批次库存拆零数">
              <a-input-number
                v-model:value="splitPackForm.batchInputCells"
                :min="0"
                :max="getBatchMaxCells()"
                :addon-after="currentSplitRecord.cellUnit"
                :disabled="true"
                style="width: 100%"
                @change="handleBatchCellsChange"
              />
              <div class="text-gray-500 text-sm mt-1">
                (原始：{{ currentSplitRecord.totalCells || 0 }}{{ currentSplitRecord.cellUnit }})
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-divider />

        <!-- 实时校验状态显示 -->
        <div class="mb-16px">
          <a-alert
            :type="getValidationStatus().isValid ? 'success' : 'warning'"
            :message="getValidationStatus().message"
            show-icon
            :style="{ marginBottom: '16px' }"
          />
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.mb-16px {
  margin-bottom: 16px;
}

.text-gray-500 {
  color: #6b7280;
}

.text-sm {
  font-size: 14px;
}

.mt-1 {
  margin-top: 4px;
}
</style>