<script setup lang="ts">
  import { Button, Col, Form, FormItem, Input, InputNumber, Popover, RangePicker, Row, Tag, Tooltip } from 'ant-design-vue'

  </script>

<!-- 拆零盒整录入模态框 -->
    <modal
      v-model:open="splitPackModalVisible"
      title="拆零盒整录入"
      width="600px"
      :ok-button-props="{ disabled: !getValidationStatus().isValid }"
      @ok="handleSplitPackConfirm"
      @cancel="handleSplitPackCancel"
    >
      <div v-if="currentSplitRecord">
        <div class="mb-16px">
          <p><strong>品名：</strong>{{ currentSplitRecord.artName }}</p>
          <p><strong>规格：</strong>{{ currentSplitRecord.artSpec }}</p>
          <p><strong>生产厂家：</strong>{{ currentSplitRecord.producer }}</p>
          <p><strong>包装规格：</strong>{{ currentSplitRecord.packCells }}{{ currentSplitRecord.cellUnit }}/{{ currentSplitRecord.packUnit }}</p>
        </div>
        <divider/>
        <Form :model="splitPackForm" layout="vertical">
          <Row :gutter="16">
            <Col :span="12">
              <FormItem label="仓库总库存整包数">
                <InputNumber
                  v-model:value="splitPackForm.deptInputPacks"
                  :min="0"
                  :max="splitPackForm.deptMaxPacks"
                  :addon-after="currentSplitRecord.packUnit"
                  :disabled="true"
                  style="width: 100%"
                  @change="handleDeptPacksChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  (原始：{{ currentSplitRecord.deptTotalPacks }}{{ currentSplitRecord.packUnit }})
                </div>
              <FormItem>
            </Col>
            <Col :span="12">
              <FormItem label="仓库总库存拆零数">
                <InputNumber
                  v-model:value="splitPackForm.deptInputCells"
                  :min="0"
                  :max="getDeptMaxCells()"
                  :addon-after="currentSplitRecord.cellUnit"
                  :disabled="true"
                  style="width: 100%"
                  @change="handleDeptCellsChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                 
                  (原始：{{ currentSplitRecord.deptTotalCells || 0 }}{{ currentSplitRecord.cellUnit }})
                </div>
              </FormItem>
            </Col>
          </Row>

          <Row :gutter="16">
            <Col :span="12">
              <FormItem label="批次库存整包数">
                <InputNumber
                  v-model:value="splitPackForm.batchInputPacks"
                  :min="currentSplitRecord.totalPacks || 0"
                  :max="Math.floor(getBatchMaxCells() / (currentSplitRecord.packCells || 1))"
                  :addon-after="currentSplitRecord.packUnit"
                  style="width: 100%"
                  @change="handleBatchPacksChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  范围：{{ currentSplitRecord.totalPacks || 0 }}-{{ Math.floor(getBatchMaxCells() / (currentSplitRecord.packCells || 1)) }}{{ currentSplitRecord.packUnit }}
                  (原始：{{ currentSplitRecord.totalPacks }}{{ currentSplitRecord.packUnit }})
                </div>
              </FormItem>
            </Col>
            <Col :span="12">
              <FormItem label="批次库存拆零数">
                <InputNumber
                  v-model:value="splitPackForm.batchInputCells"
                  :min="0"
                  :max="getBatchMaxCells()"
                  :addon-after="currentSplitRecord.cellUnit"
                  :disabled="true"
                  style="width: 100%"
                  @change="handleBatchCellsChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  (原始：{{ currentSplitRecord.totalCells || 0 }}{{ currentSplitRecord.cellUnit }})
                </div>
              </FormItem>
            </Col>
          </Row>
          <divider />
          <!-- 实时校验状态显示 -->
          <div class="mb-16px">
            <alert
              :type="getValidationStatus().isValid ? 'success' : 'warning'"
              :message="getValidationStatus().message"
              show-icon
              :style="{ marginBottom: '16px' }"
            />
          </div>
        <Form>
      </div>
    <modal>