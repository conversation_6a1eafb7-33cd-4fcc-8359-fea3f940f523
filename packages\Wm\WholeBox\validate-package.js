#!/usr/bin/env node

/**
 * WholeBox组件package.json校验脚本
 * 检查package.json配置的正确性和完整性
 */

import { readFileSync, existsSync } from 'fs'
import { resolve, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🔍 WholeBox 组件 package.json 校验\n')

let hasErrors = false
let hasWarnings = false

function logError(message) {
  console.log(`❌ 错误: ${message}`)
  hasErrors = true
}

function logWarning(message) {
  console.log(`⚠️  警告: ${message}`)
  hasWarnings = true
}

function logSuccess(message) {
  console.log(`✅ ${message}`)
}

// 读取package.json
let packageJson
try {
  const packagePath = resolve(__dirname, 'package.json')
  packageJson = JSON.parse(readFileSync(packagePath, 'utf-8'))
  logSuccess('package.json 读取成功')
} catch (error) {
  logError(`package.json 读取失败: ${error.message}`)
  process.exit(1)
}

console.log('\n📋 基本信息校验:')

// 检查必要字段
const requiredFields = {
  'name': '包名',
  'version': '版本号',
  'type': '模块类型',
  'main': '主入口',
  'module': 'ES模块入口',
  'types': '类型声明入口',
  'style': '样式入口'
}

Object.entries(requiredFields).forEach(([field, description]) => {
  if (packageJson[field]) {
    logSuccess(`${description}: ${packageJson[field]}`)
  } else {
    logError(`${description} (${field}) 缺失`)
  }
})

console.log('\n📦 包名校验:')

// 检查包名格式
if (packageJson.name) {
  if (packageJson.name.startsWith('@mh-wm/')) {
    logSuccess('包名前缀正确: @mh-wm/')
  } else {
    logError('包名前缀应为: @mh-wm/')
  }
  
  if (packageJson.name === '@mh-wm/whole-box') {
    logSuccess('包名格式正确: kebab-case')
  } else {
    logWarning('建议使用 kebab-case 格式的包名')
  }
}

console.log('\n📄 文件配置校验:')

// 检查files字段
if (packageJson.files && Array.isArray(packageJson.files)) {
  const expectedFiles = ['es', 'umd', 'src/*.d.ts', 'src/**/*.d.ts', 'index.d.ts', 'index.css']
  
  expectedFiles.forEach(file => {
    if (packageJson.files.includes(file)) {
      logSuccess(`包含文件: ${file}`)
    } else {
      logWarning(`缺少文件配置: ${file}`)
    }
  })
} else {
  logError('files 字段缺失或格式错误')
}

console.log('\n🔧 脚本配置校验:')

// 检查scripts字段
if (packageJson.scripts) {
  const expectedScripts = {
    'publish:build': 'cd ../../ && pnpm run publish:component Wm/WholeBox --no-publish',
    'publish:component': 'cd ../../ && pnpm run publish:component Wm/WholeBox'
  }
  
  Object.entries(expectedScripts).forEach(([script, expectedCommand]) => {
    if (packageJson.scripts[script]) {
      if (packageJson.scripts[script] === expectedCommand) {
        logSuccess(`脚本配置正确: ${script}`)
      } else {
        logWarning(`脚本命令可能不正确: ${script}`)
        console.log(`  期望: ${expectedCommand}`)
        console.log(`  实际: ${packageJson.scripts[script]}`)
      }
    } else {
      logError(`缺少脚本: ${script}`)
    }
  })
} else {
  logError('scripts 字段缺失')
}

console.log('\n🔗 依赖配置校验:')

// 检查peerDependencies
if (packageJson.peerDependencies) {
  const peerDepsCount = Object.keys(packageJson.peerDependencies).length
  logSuccess(`peerDependencies: ${peerDepsCount} 个依赖`)
  
  // 检查关键依赖
  const criticalPeerDeps = ['vue', 'ant-design-vue', '@idmy/core']
  criticalPeerDeps.forEach(dep => {
    if (packageJson.peerDependencies[dep]) {
      logSuccess(`关键依赖存在: ${dep}`)
    } else {
      logError(`缺少关键依赖: ${dep}`)
    }
  })
} else {
  logError('peerDependencies 字段缺失')
}

// 检查dependencies
if (packageJson.dependencies) {
  const depsCount = Object.keys(packageJson.dependencies).length
  logSuccess(`dependencies: ${depsCount} 个依赖`)
  
  // 检查业务依赖
  const businessDeps = ['@mh-wm/util']
  businessDeps.forEach(dep => {
    if (packageJson.dependencies[dep]) {
      logSuccess(`业务依赖存在: ${dep}`)
    } else {
      logWarning(`缺少业务依赖: ${dep}`)
    }
  })
} else {
  logWarning('dependencies 字段为空')
}

console.log('\n🚀 发布配置校验:')

// 检查publishConfig
if (packageJson.publishConfig) {
  if (packageJson.publishConfig.access === 'public') {
    logSuccess('发布访问权限: public')
  } else {
    logWarning('建议设置发布访问权限为 public')
  }
} else {
  logWarning('缺少 publishConfig 配置')
}

console.log('\n📁 文件存在性校验:')

// 检查关键文件是否存在
const criticalFiles = [
  'src/index.vue',
  'src/index.ts',
  'index.ts',
  'README.md',
  'examples/Basic.vue'
]

criticalFiles.forEach(file => {
  const filePath = resolve(__dirname, file)
  if (existsSync(filePath)) {
    logSuccess(`文件存在: ${file}`)
  } else {
    logError(`文件缺失: ${file}`)
  }
})

console.log('\n📊 校验结果:')

if (hasErrors) {
  console.log('❌ 发现错误，需要修复后才能正常使用')
  process.exit(1)
} else if (hasWarnings) {
  console.log('⚠️  发现警告，建议优化配置')
  console.log('✅ 基本配置正确，可以正常使用')
} else {
  console.log('✅ 所有配置都正确，package.json 完美！')
}

console.log('\n📖 使用说明:')
console.log('  构建: pnpm run publish:build')
console.log('  发布: pnpm run publish:component')
console.log('  根目录: pnpm publish:component Wm/WholeBox')
