#!/usr/bin/env node

/**
 * WholeBox组件打包服务验证脚本
 * 验证组件的完整打包流程
 */

const { execSync } = require('child_process')
const { existsSync, readdirSync, statSync, readFileSync } = require('fs')
const { resolve } = require('path')

console.log('🔍 WholeBox 组件打包服务验证\n')

const componentPath = 'packages/Wm/WholeBox'
const distPath = 'dist/Wm/WholeBox'

// 验证1: 检查组件源文件
console.log('📁 检查组件源文件...')
const sourceFiles = [
  `${componentPath}/src/index.vue`,
  `${componentPath}/src/index.ts`,
  `${componentPath}/index.ts`,
  `${componentPath}/package.json`,
  `${componentPath}/README.md`,
  `${componentPath}/examples/Basic.vue`
]

let sourceValid = true
sourceFiles.forEach(file => {
  if (existsSync(file)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file}`)
    sourceValid = false
  }
})

if (!sourceValid) {
  console.log('❌ 源文件检查失败')
  process.exit(1)
}

// 验证2: 检查package.json配置
console.log('\n📋 检查package.json配置...')
try {
  const packageJson = JSON.parse(readFileSync(`${componentPath}/package.json`, 'utf-8'))
  
  // 检查必要字段
  const requiredFields = ['name', 'version', 'main', 'module', 'types', 'style']
  requiredFields.forEach(field => {
    if (packageJson[field]) {
      console.log(`✅ ${field}: ${packageJson[field]}`)
    } else {
      console.log(`❌ ${field}: 缺失`)
    }
  })
  
  // 检查脚本
  if (packageJson.scripts && packageJson.scripts['publish:build']) {
    console.log('✅ publish:build 脚本配置正确')
  } else {
    console.log('❌ publish:build 脚本缺失')
  }
  
  if (packageJson.scripts && packageJson.scripts['publish:component']) {
    console.log('✅ publish:component 脚本配置正确')
  } else {
    console.log('❌ publish:component 脚本缺失')
  }
  
} catch (error) {
  console.log('❌ package.json 读取失败:', error.message)
  process.exit(1)
}

// 验证3: 执行构建测试
console.log('\n🔨 执行构建测试...')
try {
  console.log('执行命令: pnpm publish:component Wm/WholeBox --no-publish')
  execSync('pnpm publish:component Wm/WholeBox --no-publish', { 
    stdio: 'pipe',
    encoding: 'utf-8'
  })
  console.log('✅ 构建成功')
} catch (error) {
  console.log('❌ 构建失败')
  console.log('错误信息:', error.message)
  process.exit(1)
}

// 验证4: 检查构建产物
console.log('\n📦 检查构建产物...')
if (existsSync(distPath)) {
  console.log(`✅ 构建目录存在: ${distPath}`)
  
  // 检查关键文件
  const requiredFiles = [
    'es/index.js',
    'index.d.ts', 
    'index.css',
    'src/index.d.ts',
    'src/index.vue.d.ts'
  ]
  
  let buildValid = true
  requiredFiles.forEach(file => {
    const filePath = resolve(distPath, file)
    if (existsSync(filePath)) {
      const stats = statSync(filePath)
      console.log(`✅ ${file} (${stats.size} bytes)`)
    } else {
      console.log(`❌ ${file} - 缺失`)
      buildValid = false
    }
  })
  
  if (!buildValid) {
    console.log('❌ 构建产物不完整')
    process.exit(1)
  }
  
} else {
  console.log(`❌ 构建目录不存在: ${distPath}`)
  process.exit(1)
}

// 验证5: 检查ES模块内容
console.log('\n🔍 检查ES模块内容...')
try {
  const esModulePath = resolve(distPath, 'es/index.js')
  const esContent = readFileSync(esModulePath, 'utf-8')
  
  // 检查关键导出
  if (esContent.includes('export { WholeBox, WholeBox as default }')) {
    console.log('✅ 组件导出正确')
  } else {
    console.log('❌ 组件导出不正确')
  }
  
  // 检查依赖导入
  if (esContent.includes('from \'ant-design-vue\'')) {
    console.log('✅ ant-design-vue 依赖正确')
  } else {
    console.log('❌ ant-design-vue 依赖缺失')
  }
  
  if (esContent.includes('from \'@mh-wm/util\'')) {
    console.log('✅ @mh-wm/util 依赖正确')
  } else {
    console.log('❌ @mh-wm/util 依赖缺失')
  }
  
} catch (error) {
  console.log('❌ ES模块内容检查失败:', error.message)
}

// 验证6: 检查类型声明
console.log('\n📝 检查类型声明...')
try {
  const typesPath = resolve(distPath, 'index.d.ts')
  const typesContent = readFileSync(typesPath, 'utf-8')
  
  if (typesContent.includes('export default WholeBox')) {
    console.log('✅ 默认导出类型正确')
  } else {
    console.log('❌ 默认导出类型缺失')
  }
  
  if (typesContent.includes('export { WholeBox }')) {
    console.log('✅ 命名导出类型正确')
  } else {
    console.log('❌ 命名导出类型缺失')
  }
  
  if (typesContent.includes('WholeBoxInstance')) {
    console.log('✅ 实例类型导出正确')
  } else {
    console.log('❌ 实例类型导出缺失')
  }
  
} catch (error) {
  console.log('❌ 类型声明检查失败:', error.message)
}

console.log('\n🎉 WholeBox 组件打包服务验证完成!')
console.log('\n📖 可用命令:')
console.log('  构建: pnpm publish:component Wm/WholeBox --no-publish')
console.log('  发布: pnpm publish:component Wm/WholeBox')
console.log('  测试: cd packages/Wm/WholeBox && node test-build.js')
console.log('\n✨ 组件已准备就绪，可以正常使用和发布！')
