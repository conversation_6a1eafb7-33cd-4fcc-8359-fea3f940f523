<template>
  <div class="test-whole-box">
    <h1>WholeBox 组件测试页面</h1>
    
    <div class="test-section">
      <h2>基础测试</h2>
      <p>点击下方按钮测试拆零盒整功能：</p>
      
      <div class="test-buttons">
        <a-button type="primary" @click="testBasicSplitPack">
          测试基础拆零盒整
        </a-button>
        
        <a-button @click="testMedicineSplitPack">
          测试药品拆零
        </a-button>
        
        <a-button @click="testDeviceSplitPack">
          测试器械拆零
        </a-button>
      </div>
    </div>

    <div class="test-section">
      <h2>组件状态</h2>
      <div class="status-info">
        <p><strong>组件加载状态:</strong> {{ componentLoaded ? '已加载' : '未加载' }}</p>
        <p><strong>最后操作时间:</strong> {{ lastOperationTime || '无' }}</p>
        <p><strong>操作次数:</strong> {{ operationCount }}</p>
      </div>
    </div>

    <!-- WholeBox 组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @split-pack-success="handleSplitPackSuccess"
      @split-pack-error="handleSplitPackError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { WholeBox } from '@mh-wm/whole-box'

// 组件引用
const wholeBoxRef = ref()

// 状态数据
const componentLoaded = ref(false)
const lastOperationTime = ref('')
const operationCount = ref(0)

// 测试数据
const testRecords = {
  basic: {
    artName: '测试药品',
    artSpec: '100mg',
    producer: '测试制药公司',
    packCells: 10,
    cellUnit: '片',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8,
    batchNo: 'TEST001',
    expDate: '20251201'
  },
  medicine: {
    artName: '阿莫西林胶囊',
    artSpec: '0.25g',
    producer: '华北制药集团有限责任公司',
    packCells: 10,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 15,
    deptTotalCells: 8,
    totalPacks: 5,
    totalCells: 3,
    batchNo: '*********',
    expDate: '20251201'
  },
  device: {
    artName: '一次性注射器',
    artSpec: '5ml',
    producer: '山东威高集团医用高分子制品股份有限公司',
    packCells: 100,
    cellUnit: '支',
    packUnit: '盒',
    deptTotalPacks: 3,
    deptTotalCells: 25,
    totalPacks: 1,
    totalCells: 50,
    batchNo: '*********',
    expDate: '20261201'
  }
}

// 测试基础拆零盒整
const testBasicSplitPack = () => {
  if (!wholeBoxRef.value) {
    message.error('组件未加载')
    return
  }
  
  try {
    wholeBoxRef.value.handleSplitPack(testRecords.basic)
    updateOperationStatus('基础拆零盒整测试')
    message.info('基础拆零盒整测试已启动')
  } catch (error) {
    message.error('基础拆零盒整测试失败: ' + error.message)
  }
}

// 测试药品拆零
const testMedicineSplitPack = () => {
  if (!wholeBoxRef.value) {
    message.error('组件未加载')
    return
  }
  
  try {
    wholeBoxRef.value.handleSplitPack(testRecords.medicine)
    updateOperationStatus('药品拆零测试')
    message.info('药品拆零测试已启动')
  } catch (error) {
    message.error('药品拆零测试失败: ' + error.message)
  }
}

// 测试器械拆零
const testDeviceSplitPack = () => {
  if (!wholeBoxRef.value) {
    message.error('组件未加载')
    return
  }
  
  try {
    wholeBoxRef.value.handleSplitPack(testRecords.device)
    updateOperationStatus('器械拆零测试')
    message.info('器械拆零测试已启动')
  } catch (error) {
    message.error('器械拆零测试失败: ' + error.message)
  }
}

// 更新操作状态
const updateOperationStatus = (operation: string) => {
  lastOperationTime.value = new Date().toLocaleString()
  operationCount.value++
  console.log(`执行操作: ${operation}`)
}

// 拆零盒整成功回调
const handleSplitPackSuccess = (data: any) => {
  message.success('拆零盒整操作成功！')
  console.log('拆零盒整成功:', data)
  updateOperationStatus('拆零盒整成功')
}

// 拆零盒整失败回调
const handleSplitPackError = (error: any) => {
  message.error('拆零盒整操作失败！')
  console.error('拆零盒整失败:', error)
  updateOperationStatus('拆零盒整失败')
}

// 组件挂载后检查
onMounted(() => {
  setTimeout(() => {
    componentLoaded.value = !!wholeBoxRef.value
    if (componentLoaded.value) {
      message.success('WholeBox 组件加载成功')
    } else {
      message.error('WholeBox 组件加载失败')
    }
  }, 100)
})
</script>

<style scoped>
.test-whole-box {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 32px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #fafafa;
}

.test-section h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1890ff;
}

.test-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.status-info {
  background-color: #fff;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.status-info p {
  margin-bottom: 8px;
  font-size: 14px;
}

.status-info p:last-child {
  margin-bottom: 0;
}
</style>
