<script setup lang="ts">
import { WholeBox } from '@mh-wm/whole-box'
import { Card, Typography, Divider, Button, message, Table, Space, Tag } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, h, computed } from 'vue'
import { basicUsage, importCode, packageJsonCode, publishCommands, buildProcess } from '@/views/Wm/examples/code/WholeBoxCode'

const { Title, Paragraph, Text } = Typography

// WholeBox组件引用
const wholeBoxRef = ref()

// 操作记录列表
const operationRecords = ref([])

// 当前操作的记录
const currentRecord = ref(null)

// 模拟库存数据
const mockStockData = [
  {
    artId: 1001,
    artName: '阿莫西林胶囊',
    artSpec: '0.25g',
    producer: '华北制药集团有限责任公司',
    packCells: 10,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8,
    batchNo: 'B20240101',
    expDate: '20251201'
  },
  {
    artId: 1002,
    artName: '头孢克肟片',
    artSpec: '100mg',
    producer: '齐鲁制药有限公司',
    packCells: 6,
    cellUnit: '片',
    packUnit: '盒',
    deptTotalPacks: 8,
    deptTotalCells: 2,
    totalPacks: 3,
    totalCells: 4,
    batchNo: 'B20240102',
    expDate: '20251202'
  },
  {
    artId: 2001,
    artName: '一次性注射器',
    artSpec: '5ml',
    producer: '山东威高集团医用高分子制品股份有限公司',
    packCells: 100,
    cellUnit: '支',
    packUnit: '盒',
    deptTotalPacks: 3,
    deptTotalCells: 25,
    totalPacks: 1,
    totalCells: 50,
    batchNo: 'D20240101',
    expDate: '20261201'
  }
]

// 表格列配置
const columns = [
  {
    title: '操作时间',
    dataIndex: 'timestamp',
    key: 'timestamp',
    width: 150
  },
  {
    title: '药品名称',
    dataIndex: 'artName',
    key: 'artName',
    width: 150
  },
  {
    title: '规格',
    dataIndex: 'artSpec',
    key: 'artSpec',
    width: 100
  },
  {
    title: '包装规格',
    key: 'packSpec',
    width: 120,
    customRender: ({ record }) => {
      return h(Tag, { color: 'blue' }, () => 
        `${record.packCells}${record.cellUnit}/${record.packUnit}`
      )
    }
  },
  {
    title: '操作前库存',
    key: 'beforeStock',
    width: 150,
    customRender: ({ record }) => {
      return h('div', [
        h('div', `仓库: ${record.beforeDeptPacks}${record.packUnit}${record.beforeDeptCells}${record.cellUnit}`),
        h('div', `批次: ${record.beforeBatchPacks}${record.packUnit}${record.beforeBatchCells}${record.cellUnit}`)
      ])
    }
  },
  {
    title: '操作后库存',
    key: 'afterStock',
    width: 150,
    customRender: ({ record }) => {
      return h('div', [
        h('div', `仓库: ${record.afterDeptPacks}${record.packUnit}${record.afterDeptCells}${record.cellUnit}`),
        h('div', `批次: ${record.afterBatchPacks}${record.packUnit}${record.afterBatchCells}${record.cellUnit}`)
      ])
    }
  },
  {
    title: '操作状态',
    key: 'status',
    width: 100,
    customRender: ({ record }) => {
      return h(Tag, { 
        color: record.status === 'success' ? 'green' : 'red' 
      }, () => record.status === 'success' ? '成功' : '失败')
    }
  }
]

// 处理拆零盒整操作
const handleSplitPack = (record) => {
  currentRecord.value = record
  if (wholeBoxRef.value) {
    wholeBoxRef.value.handleSplitPack(record)
  }
}

// 模拟拆零盒整成功回调
const handleSplitPackSuccess = (data) => {
  const timestamp = new Date().toLocaleString()
  const record = {
    id: Date.now(),
    timestamp,
    ...currentRecord.value,
    beforeDeptPacks: currentRecord.value.deptTotalPacks,
    beforeDeptCells: currentRecord.value.deptTotalCells,
    beforeBatchPacks: currentRecord.value.totalPacks,
    beforeBatchCells: currentRecord.value.totalCells,
    afterDeptPacks: data.deptTotalPacks || currentRecord.value.deptTotalPacks,
    afterDeptCells: data.deptTotalCells || currentRecord.value.deptTotalCells,
    afterBatchPacks: data.totalPacks || currentRecord.value.totalPacks,
    afterBatchCells: data.totalCells || currentRecord.value.totalCells,
    status: 'success'
  }
  
  operationRecords.value.unshift(record)
  message.success('拆零盒整操作成功！')
}

// 清空操作记录
const clearRecords = () => {
  operationRecords.value = []
  message.success('操作记录已清空')
}

// 导出操作记录
const exportRecords = () => {
  if (operationRecords.value.length === 0) {
    message.warning('暂无操作记录可导出')
    return
  }
  
  const csvContent = [
    ['操作时间', '药品名称', '规格', '包装规格', '操作前仓库库存', '操作前批次库存', '操作后仓库库存', '操作后批次库存', '状态'].join(','),
    ...operationRecords.value.map(record => [
      record.timestamp,
      record.artName,
      record.artSpec,
      `${record.packCells}${record.cellUnit}/${record.packUnit}`,
      `${record.beforeDeptPacks}${record.packUnit}${record.beforeDeptCells}${record.cellUnit}`,
      `${record.beforeBatchPacks}${record.packUnit}${record.beforeBatchCells}${record.cellUnit}`,
      `${record.afterDeptPacks}${record.packUnit}${record.afterDeptCells}${record.cellUnit}`,
      `${record.afterBatchPacks}${record.packUnit}${record.afterBatchCells}${record.cellUnit}`,
      record.status === 'success' ? '成功' : '失败'
    ].join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `拆零盒整操作记录_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  
  message.success('操作记录导出成功')
}
</script>

<template>
  <Card title="基础用法 - 拆零盒整组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">拆零盒整组件</Title>
      <Paragraph>
        拆零盒整组件是一个专业的药品库存管理组件，支持仓库总库存和批次库存的整包数与拆零数互相转换。
        该组件提供了双向联动计算、实时数据校验、API集成等完整功能。
      </Paragraph>

      <!-- 模拟库存数据展示 -->
      <div class="stock-demo-section">
        <Title :level="5">模拟库存数据</Title>
        <Paragraph>
          以下是模拟的库存数据，点击"拆零盒整"按钮可以体验组件功能：
        </Paragraph>
        
        <div class="stock-cards">
          <Card 
            v-for="item in mockStockData" 
            :key="item.artId"
            size="small"
            class="stock-card"
          >
            <template #title>
              <div class="stock-title">
                {{ item.artName }}
                <Tag color="blue">{{ item.artSpec }}</Tag>
              </div>
            </template>
            
            <div class="stock-info">
              <p><strong>生产厂家：</strong>{{ item.producer }}</p>
              <p><strong>包装规格：</strong>{{ item.packCells }}{{ item.cellUnit }}/{{ item.packUnit }}</p>
              <p><strong>仓库总库存：</strong>{{ item.deptTotalPacks }}{{ item.packUnit }}{{ item.deptTotalCells }}{{ item.cellUnit }}</p>
              <p><strong>批次库存：</strong>{{ item.totalPacks }}{{ item.packUnit }}{{ item.totalCells }}{{ item.cellUnit }}</p>
              <p><strong>批次号：</strong>{{ item.batchNo }}</p>
            </div>
            
            <template #actions>
              <Button 
                type="primary" 
                size="small"
                @click="handleSplitPack(item)"
              >
                拆零盒整
              </Button>
            </template>
          </Card>
        </div>
      </div>

      <Divider />

      <!-- 操作记录 -->
      <div class="records-section">
        <div class="records-header">
          <Title :level="5">操作记录</Title>
          <Space>
            <Button @click="clearRecords" size="small">
              清空记录
            </Button>
            <Button @click="exportRecords" size="small">
              导出记录
            </Button>
          </Space>
        </div>
        
        <Table
          :dataSource="operationRecords"
          :columns="columns"
          :pagination="{ pageSize: 5 }"
          size="small"
          :scroll="{ x: 1000 }"
        >
          <template #emptyText>
            <div class="empty-records">
              <p>暂无操作记录</p>
              <p>点击上方"拆零盒整"按钮开始体验</p>
            </div>
          </template>
        </Table>
      </div>

      <Divider />

      <!-- 代码示例 -->
      <div class="code-section">
        <Title :level="5">代码示例</Title>
        
        <CodeDemoVue 
          title="基础用法"
          :code="basicUsage"
          description="最基本的拆零盒整组件使用方法"
        />
        
        <CodeDemoVue 
          title="组件导入"
          :code="importCode"
          description="如何在项目中导入和使用WholeBox组件"
        />
        
        <CodeDemoVue 
          title="package.json配置"
          :code="packageJsonCode"
          description="项目依赖配置"
        />
        
        <CodeDemoVue 
          title="打包发布命令"
          :code="publishCommands"
          description="组件打包和发布相关命令"
        />
        
        <CodeDemoVue 
          title="构建流程说明"
          :code="buildProcess"
          description="组件构建流程详细说明"
        />
      </div>
    </div>

    <!-- 使用拆零盒整组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @split-pack-success="handleSplitPackSuccess"
    />
  </Card>
</template>

<style scoped>
.stock-demo-section {
  margin-bottom: 24px;
}

.stock-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.stock-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.stock-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.stock-info p {
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.records-section {
  margin-bottom: 24px;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.empty-records {
  text-align: center;
  color: #999;
  padding: 20px;
}

.code-section {
  margin-top: 24px;
}

:deep(.ant-card-actions) {
  background-color: #fafafa;
}

:deep(.ant-card-actions > li) {
  margin: 8px 0;
}
</style>
