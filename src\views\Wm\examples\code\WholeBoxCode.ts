import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <div>
    <!-- 拆零盒整按钮 -->
    <a-button type="primary" @click="handleOpenSplitPack">
      拆零盒整
    </a-button>
    
    <!-- 拆零盒整组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @split-pack-success="handleSplitPackSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// 库存记录数据
const stockRecord = {
  artName: '阿莫西林胶囊',
  artSpec: '0.25g',
  producer: '华北制药',
  packCells: 10,        // 包装规格：10粒/盒
  cellUnit: '粒',       // 拆零单位
  packUnit: '盒',       // 包装单位
  deptTotalPacks: 5,    // 仓库总库存整包数
  deptTotalCells: 3,    // 仓库总库存拆零数
  totalPacks: 2,        // 批次库存整包数
  totalCells: 8,        // 批次库存拆零数
}

// 打开拆零盒整模态框
const handleOpenSplitPack = () => {
  wholeBoxRef.value?.handleSplitPack(stockRecord)
}

// 拆零盒整成功回调
const handleSplitPackSuccess = (data) => {
  console.log('拆零盒整操作成功:', data)
  // 处理成功后的逻辑，如刷新数据、显示提示等
}
</script>`)

// 组件导入
export const importCode = wrapCodeExample(`// 方式1: 默认导入
import WholeBox from '@mh-wm/whole-box'

// 方式2: 命名导入
import { WholeBox } from '@mh-wm/whole-box'

// 方式3: 导入类型
import type { WholeBoxInstance } from '@mh-wm/whole-box'

// 在组件中使用
export default {
  components: {
    WholeBox
  }
}

// 或在setup中使用
<script setup>
import { WholeBox } from '@mh-wm/whole-box'
</script>`)

// package.json配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/whole-box": "^1.0.1",
    "@mh-wm/util": "^1.0.6",
    "ant-design-vue": "^4.2.6",
    "vue": "^3.5.13"
  }
}`

// 组件方法调用
export const methodsUsage = wrapCodeExample(`<template>
  <div>
    <!-- 拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />
    
    <!-- 操作按钮 -->
    <div class="actions">
      <a-button @click="openSplitPack">打开拆零盒整</a-button>
      <a-button @click="getComponentData">获取组件数据</a-button>
      <a-button @click="validateForm">验证表单</a-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// 打开拆零盒整模态框
const openSplitPack = () => {
  const record = {
    artName: '测试药品',
    artSpec: '100mg',
    packCells: 10,
    cellUnit: '片',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8
  }
  
  wholeBoxRef.value.handleSplitPack(record)
}

// 获取组件数据
const getComponentData = () => {
  const data = wholeBoxRef.value.getCurrentData()
  console.log('当前组件数据:', data)
}

// 验证表单
const validateForm = () => {
  const isValid = wholeBoxRef.value.validateForm()
  console.log('表单验证结果:', isValid)
}
</script>`)

// 表格集成示例
export const tableIntegration = wrapCodeExample(`<template>
  <div>
    <!-- 库存数据表格 -->
    <a-table
      :columns="columns"
      :data-source="stockData"
      row-key="artId"
    >
      <!-- 操作列 -->
      <template #action="{ record }">
        <a-button 
          type="primary" 
          size="small"
          @click="handleSplitPack(record)"
        >
          拆零盒整
        </a-button>
      </template>
    </a-table>
    
    <!-- 拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" @split-pack-success="handleSuccess" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()
const stockData = ref([
  {
    artId: 1001,
    artName: '阿莫西林胶囊',
    artSpec: '0.25g',
    packCells: 10,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8
  }
])

const columns = [
  { title: '药品名称', dataIndex: 'artName' },
  { title: '规格', dataIndex: 'artSpec' },
  { title: '仓库库存', key: 'deptStock' },
  { title: '批次库存', key: 'batchStock' },
  { title: '操作', key: 'action', slots: { customRender: 'action' } }
]

// 拆零盒整操作
const handleSplitPack = (record) => {
  wholeBoxRef.value.handleSplitPack(record)
}

// 操作成功回调
const handleSuccess = (data) => {
  // 刷新表格数据
  refreshTableData()
}
</script>`)

// API集成示例
export const apiIntegration = wrapCodeExample(`<template>
  <div>
    <!-- API状态监控 -->
    <div class="api-status">
      <a-tag :color="apiStatus.connected ? 'green' : 'red'">
        {{ apiStatus.connected ? 'API已连接' : 'API未连接' }}
      </a-tag>
      <span>成功: {{ apiStatus.successCount }}</span>
      <span>失败: {{ apiStatus.errorCount }}</span>
    </div>
    
    <!-- 拆零盒整组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @api-call="handleApiCall"
      @split-pack-success="handleSuccess"
      @split-pack-error="handleError"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// API状态
const apiStatus = reactive({
  connected: true,
  successCount: 0,
  errorCount: 0,
  lastCall: null
})

// 处理API调用
const handleApiCall = (event) => {
  apiStatus.lastCall = new Date()
  
  if (event.type === 'success') {
    apiStatus.successCount++
  } else if (event.type === 'error') {
    apiStatus.errorCount++
  }
}

// 操作成功
const handleSuccess = (data) => {
  console.log('拆零盒整成功:', data)
}

// 操作失败
const handleError = (error) => {
  console.error('拆零盒整失败:', error)
}
</script>`)

// 打包发布指令
export const publishCommands = `# 在项目根目录下执行以下命令打包并发布组件

# 正式版本
pnpm publish:component Wm/WholeBox

# 测试版本
pnpm publish:test-component Wm/WholeBox

# 开发版本
pnpm publish:dev-component Wm/WholeBox

# Beta版本
pnpm publish:beta-component Wm/WholeBox

# 仅构建不发布
pnpm publish:component Wm/WholeBox --no-publish

# 安装组件
pnpm add @mh-wm/whole-box`

// 打包流程说明
export const buildProcess = `打包命令会执行以下操作：
1. 编译组件源码（TypeScript + Vue）
2. 生成类型声明文件（.d.ts）
3. 打包CSS样式文件
4. 生成ES模块和UMD格式
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

构建产物结构：
dist/Wm/WholeBox/
├── es/index.js          # ES模块格式
├── umd/index.js         # UMD格式
├── src/index.d.ts       # 类型声明
├── index.d.ts           # 主入口类型
├── index.css            # 样式文件
└── package.json         # 包配置`

// 高级配置
export const advancedConfig = wrapCodeExample(`<template>
  <div>
    <!-- 高级配置示例 -->
    <WholeBox 
      ref="wholeBoxRef"
      :api-config="apiConfig"
      :validation-rules="validationRules"
      :ui-config="uiConfig"
      @split-pack-success="handleSuccess"
      @split-pack-error="handleError"
      @validation-error="handleValidationError"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// API配置
const apiConfig = {
  baseURL: '/api/wm',
  timeout: 10000,
  retryCount: 3,
  headers: {
    'Content-Type': 'application/json'
  }
}

// 验证规则配置
const validationRules = {
  required: true,
  minPackCount: 0,
  maxPackCount: 9999,
  minCellCount: 0,
  allowNegative: false
}

// UI配置
const uiConfig = {
  modalWidth: '800px',
  showBatchInfo: true,
  showValidationStatus: true,
  theme: 'default'
}

// 事件处理
const handleSuccess = (data) => {
  console.log('操作成功:', data)
}

const handleError = (error) => {
  console.error('操作失败:', error)
}

const handleValidationError = (errors) => {
  console.warn('验证失败:', errors)
}
</script>`)
